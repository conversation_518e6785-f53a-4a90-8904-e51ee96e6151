import * as fs from 'fs/promises';
import * as path from 'path';
import { createReadStream, createWriteStream } from 'fs';
import { pipeline } from 'stream/promises';

export interface LocalFileInfo {
  filename: string;
  basename: string;
  lastmod: string;
  size: number;
  type: 'file' | 'directory';
  etag?: string;
  mime?: string;
}

export class LocalFileHandler {
  private basePath: string;

  constructor(basePath: string) {
    this.basePath = path.resolve(basePath);
  }

  /**
   * Get directory contents (equivalent to webdav.getDirectoryContents)
   */
  async getDirectoryContents(dirPath: string = '/'): Promise<LocalFileInfo[]> {
    const fullPath = this.getFullPath(dirPath);
    
    try {
      const entries = await fs.readdir(fullPath, { withFileTypes: true });
      const results: LocalFileInfo[] = [];

      for (const entry of entries) {
        const entryPath = path.join(fullPath, entry.name);
        const stats = await fs.stat(entryPath);
        const relativePath = path.posix.join(dirPath, entry.name);

        results.push({
          filename: relativePath,
          basename: entry.name,
          lastmod: stats.mtime.toISOString(),
          size: entry.isDirectory() ? 0 : stats.size,
          type: entry.isDirectory() ? 'directory' : 'file',
          etag: `"${stats.mtime.getTime()}-${stats.size}"`,
          mime: entry.isDirectory() ? 'httpd/unix-directory' : this.getMimeType(entry.name)
        });
      }

      return results;
    } catch (error) {
      throw new Error(`Failed to read directory ${dirPath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if a file or directory exists
   */
  async exists(filePath: string): Promise<boolean> {
    try {
      await fs.access(this.getFullPath(filePath));
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get file stats
   */
  async stat(filePath: string): Promise<LocalFileInfo> {
    const fullPath = this.getFullPath(filePath);
    
    try {
      const stats = await fs.stat(fullPath);
      const basename = path.basename(filePath);

      return {
        filename: filePath,
        basename,
        lastmod: stats.mtime.toISOString(),
        size: stats.isDirectory() ? 0 : stats.size,
        type: stats.isDirectory() ? 'directory' : 'file',
        etag: `"${stats.mtime.getTime()}-${stats.size}"`,
        mime: stats.isDirectory() ? 'httpd/unix-directory' : this.getMimeType(basename)
      };
    } catch (error) {
      throw new Error(`Failed to stat ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Read file content as buffer
   */
  async getFileContents(filePath: string): Promise<Buffer> {
    const fullPath = this.getFullPath(filePath);
    
    try {
      return await fs.readFile(fullPath);
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Write file content from buffer
   */
  async putFileContents(filePath: string, data: Buffer | string): Promise<void> {
    const fullPath = this.getFullPath(filePath);
    
    try {
      // Ensure directory exists
      await fs.mkdir(path.dirname(fullPath), { recursive: true });
      await fs.writeFile(fullPath, data);
    } catch (error) {
      throw new Error(`Failed to write file ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create directory
   */
  async createDirectory(dirPath: string): Promise<void> {
    const fullPath = this.getFullPath(dirPath);
    
    try {
      await fs.mkdir(fullPath, { recursive: true });
    } catch (error) {
      throw new Error(`Failed to create directory ${dirPath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete file or directory
   */
  async deleteFile(filePath: string): Promise<void> {
    const fullPath = this.getFullPath(filePath);
    
    try {
      const stats = await fs.stat(fullPath);
      if (stats.isDirectory()) {
        await fs.rmdir(fullPath, { recursive: true });
      } else {
        await fs.unlink(fullPath);
      }
    } catch (error) {
      throw new Error(`Failed to delete ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Move/rename file or directory
   */
  async moveFile(fromPath: string, toPath: string): Promise<void> {
    const fullFromPath = this.getFullPath(fromPath);
    const fullToPath = this.getFullPath(toPath);
    
    try {
      // Ensure destination directory exists
      await fs.mkdir(path.dirname(fullToPath), { recursive: true });
      await fs.rename(fullFromPath, fullToPath);
    } catch (error) {
      throw new Error(`Failed to move ${fromPath} to ${toPath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Copy file
   */
  async copyFile(fromPath: string, toPath: string): Promise<void> {
    const fullFromPath = this.getFullPath(fromPath);
    const fullToPath = this.getFullPath(toPath);
    
    try {
      // Ensure destination directory exists
      await fs.mkdir(path.dirname(fullToPath), { recursive: true });
      await fs.copyFile(fullFromPath, fullToPath);
    } catch (error) {
      throw new Error(`Failed to copy ${fromPath} to ${toPath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Calculate directory size recursively
   */
  async calculateDirectorySize(dirPath: string = '/'): Promise<number> {
    const fullPath = this.getFullPath(dirPath);
    
    try {
      const stats = await fs.stat(fullPath);
      
      if (stats.isFile()) {
        return stats.size;
      }
      
      if (stats.isDirectory()) {
        const entries = await fs.readdir(fullPath, { withFileTypes: true });
        let totalSize = 0;
        
        for (const entry of entries) {
          const entryPath = path.join(dirPath, entry.name);
          totalSize += await this.calculateDirectorySize(entryPath);
        }
        
        return totalSize;
      }
      
      return 0;
    } catch (error) {
      console.error(`Error calculating size for ${dirPath}:`, error);
      return 0;
    }
  }

  /**
   * Get full system path from relative path
   */
  private getFullPath(relativePath: string): string {
    // Normalize the path and ensure it's within the base path
    const normalizedPath = path.posix.normalize(relativePath);
    const fullPath = path.resolve(this.basePath, normalizedPath.startsWith('/') ? normalizedPath.slice(1) : normalizedPath);
    
    // Security check: ensure the resolved path is within the base path
    if (!fullPath.startsWith(this.basePath)) {
      throw new Error('Path traversal attempt detected');
    }
    
    return fullPath;
  }

  /**
   * Get MIME type based on file extension
   */
  private getMimeType(filename: string): string {
    const ext = path.extname(filename).toLowerCase();
    const mimeTypes: { [key: string]: string } = {
      '.txt': 'text/plain',
      '.html': 'text/html',
      '.css': 'text/css',
      '.js': 'application/javascript',
      '.json': 'application/json',
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      '.ppt': 'application/vnd.ms-powerpoint',
      '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.mp4': 'video/mp4',
      '.mp3': 'audio/mpeg',
      '.zip': 'application/zip',
      '.rar': 'application/x-rar-compressed',
      '.7z': 'application/x-7z-compressed'
    };
    
    return mimeTypes[ext] || 'application/octet-stream';
  }
}
