import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, readBody } from 'h3';
import * as fs from 'fs/promises';
import * as path from 'path';
import { createClient } from 'webdav';

export default defineEventHandler(async (event) => {
  const accountId = event.context.params?.accountId;

  if (!accountId) {
    return { error: 'Account ID is required' };
  }

  try {
    const accountsFilePath = path.resolve(process.cwd(), 'data', 'accounts.json');
    const accountsData = await fs.readFile(accountsFilePath, 'utf-8');
    const accounts = JSON.parse(accountsData);

    const account = accounts.find((acc: any) => acc.id === accountId);

    if (!account) {
      return { error: 'Account not found' };
    }

    if (!account.url) {
      return { error: 'Account does not have a URL for file access' };
    }

    const body = await readBody(event);
    const { path: folderPath } = body;

    if (!folderPath) {
      return { error: 'Folder path is required' };
    }

    // Validate folder path
    if (folderPath.includes('..') || folderPath.includes('\\')) {
      return { error: 'Invalid folder path' };
    }

    const webdavClient = createClient(account.url, {
      username: account.name,
      password: account.password,
    });

    // Create the directory
    await webdavClient.createDirectory(folderPath);

    return { 
      message: 'Folder created successfully',
      path: folderPath
    };

  } catch (error) {
    console.error(`Error creating folder for account ${accountId}:`, error);
    return { error: `Failed to create folder: ${(error as Error).message}` };
  }
});
