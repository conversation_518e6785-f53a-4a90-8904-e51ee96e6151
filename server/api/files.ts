import { define<PERSON><PERSON><PERSON>and<PERSON> } from 'h3';
import * as fs from 'fs/promises';
import * as path from 'path';
import { createClient } from 'webdav';
import { LocalFileHandler } from '~/server/utils/localFileHandler';

export const fileCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

export default defineEventHandler(async (event) => {
  const accountId = event.context.params?.accountId;
  const filePath = event.context.query?.path || '/'; // Get path from query, default to root

  if (!accountId) {
    return { error: 'Account ID is required' };
  }

  const cacheKey = `${accountId}-${filePath}`;
  const cachedResponse = fileCache.get(cacheKey);

  if (cachedResponse && Date.now() < cachedResponse.timestamp + CACHE_TTL) {
    return cachedResponse.data;
  }

  try {
    const accountsFilePath = path.resolve(process.cwd(), 'data', 'accounts.json');
    const accountsData = await fs.readFile(accountsFilePath, 'utf-8');
    const accounts = JSON.parse(accountsData);

    const account = accounts.find((acc: any) => acc.id === accountId);

    if (!account) {
      return { error: 'Account not found' };
    }

    let files;

    if (account.provider === 'local') {
      if (!account.localPath) {
        return { error: 'Local account does not have a local path configured' };
      }

      try {
        const localHandler = new LocalFileHandler(account.localPath);
        const localFiles = await localHandler.getDirectoryContents(filePath);

        // Convert local file format to match WebDAV format
        files = localFiles.map(file => ({
          filename: file.filename,
          basename: file.basename,
          lastmod: file.lastmod,
          size: file.size,
          type: file.type,
          etag: file.etag,
          mime: file.mime
        }));
      } catch (error) {
        console.error('Error reading local directory:', error);
        return { error: `Failed to read local directory: ${error instanceof Error ? error.message : 'Unknown error'}` };
      }
    } else if (account.provider === 'webdav') {
      if (!account.url) {
        return { error: 'WebDAV account does not have a URL for file access' };
      }

      try {
        const webdavClient = createClient(
          account.url,
          {
            username: account.username || account.name, // Support both username and name fields
            password: account.password,
          }
        );

        const directoryItems = await webdavClient.getDirectoryContents(filePath);

        // Ensure directoryItems is an array before mapping
        const itemsToMap = Array.isArray(directoryItems) ? directoryItems : (directoryItems as any).data;

        files = itemsToMap.map((item: any) => ({
          name: item.basename,
          type: item.type === 'file' ? getFileType(item.basename) : item.type,
          path: item.filename,
          size: item.size,
          lastModified: item.lastmod,
          url: item.type === 'file' ? `${account.url}${item.filename}` : undefined, // Direct URL for files
        }));
      } catch (error) {
        console.error('Error fetching files from WebDAV:', error);
        return { error: 'Failed to fetch files from WebDAV server' };
      }
    } else {
      return { error: 'Invalid account provider type' };
    }

    const responseData = { files };
    fileCache.set(cacheKey, { data: responseData, timestamp: Date.now() });
    return responseData;

  } catch (error) {
    console.error(`Error fetching files for account ${accountId} at path ${filePath}:`, error);
    return { error: `Failed to fetch files for account ${accountId} at path ${filePath}: ${(error as Error).message}` };
  }
});

// Helper function to determine file type based on extension
function getFileType(filename: string): string {
  const ext = path.extname(filename).toLowerCase();
  if (['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt', '.csv', '.xlsx', '.xls', '.ppt', '.pptx', '.json', '.xml', '.md'].includes(ext)) {
    return 'document';
  } else if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp', '.tiff', '.ico'].includes(ext)) {
    return 'image';
  } else if (['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v'].includes(ext)) {
    return 'video';
  } else if (['.mp3', '.wav', '.ogg', '.flac', '.aac', '.m4a'].includes(ext)) {
    return 'audio';
  }
  return 'other';
}