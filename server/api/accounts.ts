import { defineEvent<PERSON>and<PERSON> } from 'h3';
import * as fs from 'fs/promises';
import * as path from 'path';

// 1. Define an interface for your account object.
// This describes the data structure in your 'accounts.json' file.
interface Account {
  id: string;
  name: string;
  provider: 'webdav' | 'local';
  url?: string; // Required for webdav, not used for local
  username?: string; // Required for webdav, not used for local
  password?: string; // Required for webdav, not used for local
  localPath?: string; // Required for local, not used for webdav
  email?: string;
  quotaLimit?: number;
  // You can add any other properties that exist in your JSON file here
  [key: string]: any; // Allows for other dynamic properties if needed
}

// Optional: Define the shape of the account object after processing
interface AccountWithStatus extends Account {
  isConnected: boolean;
  statusCode: number | null;
  connectionError?: string;
}


export default defineEventHandler(async (event) => {
  try {
    const filePath = path.resolve(process.cwd(), 'data', 'accounts.json');
    const data = await fs.readFile(filePath, 'utf-8');
    
    // 2. Assert the type of the parsed JSON data.
    // This tells TypeScript what to expect, resolving the 'any' type issue.
    const accounts: Account[] = JSON.parse(data);

    const accountsWithStatus: AccountWithStatus[] = await Promise.all(accounts.map(async (account) => {
      // Now, TypeScript knows `account` is of type `Account`
      const processedAccount: Partial<AccountWithStatus> = { ...account };

      // Check connection based on provider type
      if (account.provider === 'webdav' && processedAccount.url) {
        try {
          const headers = new Headers();
          if (processedAccount.username && processedAccount.password) {
            const credentials = btoa(`${processedAccount.username}:${processedAccount.password}`);
            headers.append('Authorization', `Basic ${credentials}`);
          }
          const response = await fetch(processedAccount.url, { method: 'GET', headers: headers });
          processedAccount.isConnected = response.ok;
          processedAccount.statusCode = response.status;
        } catch (e: any) { // It's good practice to type the catch block error
          console.error(`Error checking WebDAV connection for ${processedAccount.url}:`, e);
          processedAccount.isConnected = false;
          processedAccount.statusCode = null;
          processedAccount.connectionError = e.message;
        }
      } else if (account.provider === 'local' && account.localPath) {
        try {
          // Check if local path exists and is accessible
          const stats = await fs.stat(account.localPath);
          processedAccount.isConnected = stats.isDirectory();
          processedAccount.statusCode = processedAccount.isConnected ? 200 : 404;
        } catch (e: any) {
          console.error(`Error checking local path for ${account.localPath}:`, e);
          processedAccount.isConnected = false;
          processedAccount.statusCode = 404;
          processedAccount.connectionError = e.message;
        }
      } else {
        // Missing required connection details
        processedAccount.isConnected = false;
        processedAccount.statusCode = null;
        processedAccount.connectionError = 'Missing connection details';
      }

      return processedAccount as AccountWithStatus;
    }));

    // With `accounts` now typed as `Account[]`, TypeScript can infer the types here.
    // `sum` is inferred as `number` from the initial value `0`.
    // `account` is inferred as `Account`.
    const totalQuotaLimit = accounts.reduce((sum, account) => {
      return sum + (account.quotaLimit || 0);
    }, 0);

    return { accounts: accountsWithStatus, totalQuotaLimit };
  } catch (error) {
    console.error('Error fetching accounts:', error);
    // It's good practice to provide a more specific error response in production
    // For example, using `createError` from h3/error
    return { error: 'Failed to fetch accounts' };
  }
});