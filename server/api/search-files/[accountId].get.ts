import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, getQuery } from 'h3';
import * as fs from 'fs/promises';
import * as path from 'path';
import { createClient } from 'webdav';
import { LRUCache } from 'lru-cache';

// Define a cache for directory contents
const directoryCache = new LRUCache<string, any[]>({
  max: 500, // Maximum number of directory entries to cache
  ttl: 1000 * 60 * 5, // 5 minutes TTL
});

interface TraversalOptions {
  maxDepth?: number;
  signal?: AbortSignal;
}

// Helper function to iteratively traverse directory contents
async function* traverseDirectoryContents(
  client: any,
  currentPath: string,
  accountId: string,
  options: TraversalOptions = {},
  currentDepth: number = 0
): AsyncGenerator<any> {
  const { maxDepth, signal } = options;

  if (signal?.aborted) {
    return;
  }

  if (maxDepth !== undefined && currentDepth > maxDepth) {
    return;
  }

  let directoryItems: any[];

  // Create account-specific cache key to prevent cross-account cache pollution
  const cacheKey = `${accountId}:${currentPath}`;

  // Try to get from cache first
  if (directoryCache.has(cacheKey)) {
    directoryItems = directoryCache.get(cacheKey)!;
    // console.log(`Cache hit for ${cacheKey}`);
  } else {
    try {
      directoryItems = await client.getDirectoryContents(currentPath);
      directoryCache.set(cacheKey, directoryItems); // Cache the result with account-specific key
      // console.log(`Cache miss for ${cacheKey}, fetched and cached.`);
    } catch (error) {
      console.warn(`Could not read directory contents for ${currentPath}: ${(error as Error).message}`);
      return; // Stop traversal for this path if it cannot be read
    }
  }

  const itemsToProcess = Array.isArray(directoryItems) ? directoryItems : (directoryItems as any).data;

  for (const item of itemsToProcess) {
    if (signal?.aborted) {
      return;
    }
    yield item;
    if (item.type === 'directory') {
      yield* traverseDirectoryContents(client, item.filename, accountId, options, currentDepth + 1);
    }
  }
}

export default defineEventHandler(async (event) => {
  const accountId = event.context.params?.accountId;
  const searchQuery = getQuery(event).query as string || '';
  const fileType = getQuery(event).type as string || '';
  const page = parseInt(getQuery(event).page as string) || 1;
  const limit = parseInt(getQuery(event).limit as string) || 10;

  if (!accountId) {
    return { error: 'Account ID is required' };
  }


  try {
    const accountsFilePath = path.resolve(process.cwd(), 'data', 'accounts.json');
    const accountsData = await fs.readFile(accountsFilePath, 'utf-8');
    const accounts = JSON.parse(accountsData);

    const account = accounts.find((acc: any) => acc.id === accountId);

    if (!account) {
      return { error: 'Account not found' };
    }

    if (!account.url) {
      return { error: 'Account does not have a URL for file access' };
    }

    const webdavClient = createClient(
      account.url,
      {
        username: account.name,
        password: account.password,
      }
    );

    console.log(`Traversing directory contents for account ${accountId} with query "${searchQuery}" and type "${fileType}"...`);

    const controller = new AbortController();
    const signal = controller.signal;

    const normalizedSearchQuery = (searchQuery || '').normalize('NFKC').toLowerCase();
    const foundItems: any[] = [];
    let totalCount = 0;
    const maxResults = page * limit + 100; // Buffer for better pagination

    try {
      for await (const item of traverseDirectoryContents(webdavClient, '/', accountId, { maxDepth: 5, signal })) {
        totalCount++; // Count all items traversed, not just filtered ones

        const rawFilename = item.basename || '';
        const normalizedFilename = rawFilename.normalize('NFKC').toLowerCase();
        const matchesSearch = normalizedFilename.includes(normalizedSearchQuery);
        const matchesType = fileType === '' || (item.type === 'file' && getFileType(item.basename) === fileType);

        if (matchesSearch && matchesType) {
          foundItems.push(item);
        }

        // Early termination for better performance when we have enough results
        if (foundItems.length >= maxResults) {
          console.log(`Early termination: Found ${foundItems.length} results, stopping search.`);
          break;
        }
      }
    } catch (error) {
      if (error instanceof DOMException && error.name === 'AbortError') {
        console.log('Search aborted by client or server.');
      } else {
        throw error; // Re-throw other errors
      }
    }

    console.log(`Found ${foundItems.length} matching items out of ${totalCount} traversed.`);

    // Implement pagination on the found items
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedItems = foundItems.slice(startIndex, endIndex);

    const filesAndFolders = paginatedItems.map((item: any) => ({
      id: item.filename,
      name: item.basename,
      type: item.type === 'file' ? getFileType(item.basename) : item.type,
      path: item.filename,
      size: item.size,
      lastModified: item.lastmod,
      url: item.type === 'file' ? `${account.url}${item.filename}` : undefined,
    }));

    return {
      files: filesAndFolders,
      totalFiles: foundItems.length, // total matching files
      currentPage: page,
      totalPages: Math.ceil(foundItems.length / limit),
    };

  } catch (error) {
    console.error(`Error searching files for account ${accountId} with query "${searchQuery}":`, error);
    return { error: `Failed to search files for account ${accountId}: ${(error as Error).message}` };
  }
});

// Helper function to determine file type based on extension
function getFileType(filename: string): string {
  const ext = path.extname(filename).toLowerCase();
  if (['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt', '.csv', '.xlsx', '.xls', '.ppt', '.pptx', '.json', '.xml', '.md'].includes(ext)) {
    return 'document';
  } else if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp', '.tiff', '.ico'].includes(ext)) {
    return 'image';
  } else if (['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v'].includes(ext)) {
    return 'video';
  } else if (['.mp3', '.wav', '.ogg', '.flac', '.aac', '.m4a'].includes(ext)) {
    return 'audio';
  }
  return 'other';
}
