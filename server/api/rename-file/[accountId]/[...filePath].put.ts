import { define<PERSON>vent<PERSON><PERSON><PERSON>, readBody } from 'h3';
import * as fs from 'fs/promises';
import * as path from 'path';
import { createClient } from 'webdav';
import { fileCache } from '../../files'; // Import the fileCache for cache invalidation

export default defineEventHandler(async (event) => {
  const accountId = event.context.params?.accountId;
  let oldFilePath = event.context.params?.filePath;

  if (!accountId) {
    console.error('Validation Error: Account ID is required');
    event.node.res.statusCode = 400;
    return { error: 'Account ID is required' };
  }
  if (!oldFilePath) {
    console.error('Validation Error: Current file path is required');
    event.node.res.statusCode = 400;
    return { error: 'Current file path is required' };
  }

  let { newPath } = await readBody(event);
  if (!newPath) {
    console.error('Validation Error: New path is required');
    event.node.res.statusCode = 400;
    return { error: 'New path is required' };
  }

  // Ensure paths are strings, joining if they are arrays from dynamic routes
  if (Array.isArray(oldFilePath)) {
    oldFilePath = '/' + oldFilePath.join('/');
  }

  // Decode the URL-encoded path that comes from the frontend
  oldFilePath = decodeURIComponent(oldFilePath);

  // Ensure paths start with '/' and normalize them
  if (!oldFilePath.startsWith('/')) {
    oldFilePath = '/' + oldFilePath;
  }
  if (!newPath.startsWith('/')) {
    newPath = '/' + newPath;
  }

  // Normalize paths to remove double slashes
  oldFilePath = oldFilePath.replace(/\/\/+/g, '/');
  newPath = newPath.replace(/\/\/+/g, '/');

  console.log(`Debug: Processed oldFilePath: ${oldFilePath}`);
  console.log(`Debug: Processed newPath: ${newPath}`);

  try {
    const accountsFilePath = path.resolve(process.cwd(), 'data', 'accounts.json');
    const accountsData = await fs.readFile(accountsFilePath, 'utf-8');
    const accounts = JSON.parse(accountsData);

    const account = accounts.find((acc: any) => acc.id === accountId);
    if (!account) {
      console.error(`Validation Error: Account not found for ID: ${accountId}`);
      event.node.res.statusCode = 404;
      return { error: 'Account not found' };
    }

    if (!account.url) {
      console.error(`Validation Error: Account ${accountId} does not have a URL for file access`);
      event.node.res.statusCode = 400;
      return { error: 'Account does not have a URL for file access' };
    }

    console.log(`Debug: Account details for ${accountId}: URL=${account.url}, Username=${account.name}`);

    const webdavClient = createClient(
      account.url,
      {
        username: account.name,
        password: account.password,
      }
    );

    console.log(`Attempting to rename file for account ${accountId}:`);
    console.log(`  Old Path: ${oldFilePath}`);
    console.log(`  New Path: ${newPath}`);

    // Check if the old file exists before attempting to rename
    try {
      const exists = await webdavClient.exists(oldFilePath);
      if (!exists) {
        console.error(`File not found: ${oldFilePath}`);
        event.node.res.statusCode = 404;
        return { error: `File not found: ${oldFilePath}` };
      }
    } catch (checkError) {
      console.error(`Error checking file existence: ${checkError}`);
      event.node.res.statusCode = 500;
      return { error: 'Failed to verify file existence' };
    }

    // WebDAV uses MOVE for renaming files
    await webdavClient.moveFile(oldFilePath, newPath);

    // Clear file cache for this account to ensure fresh data on next request
    const cacheKeys = Array.from(fileCache.keys());
    cacheKeys.forEach(key => {
      if (key.includes(accountId)) {
        fileCache.delete(key);
      }
    });

    console.log(`File renamed successfully for account ${accountId}: ${oldFilePath} -> ${newPath}`);
    return {
      success: true,
      message: `File renamed successfully`,
      oldPath: oldFilePath,
      newPath: newPath
    };
  } catch (error) {
    const errorMessage = (error as Error).message;
    const errorCode = (error as any).code; // Common for Node.js errors
    const errorResponse = (error as any).response; // Common for HTTP errors from libraries like webdav
    const statusCode = errorResponse?.status || 500;

    console.error(`Error renaming file for account ${accountId} from ${oldFilePath} to ${newPath}:`);
    console.error(`  Message: ${errorMessage}`);
    if (errorCode) {
      console.error(`  Code: ${errorCode}`);
    }
    if (errorResponse) {
      console.error(`  Response Status: ${errorResponse.status}`);
      console.error(`  Response Data: ${JSON.stringify(errorResponse.data)}`);
    }
    console.error(`  Full Error Object:`, error);

    // Set appropriate HTTP status code
    event.node.res.statusCode = statusCode;

    return {
      error: `Failed to rename file: ${errorMessage}`,
      details: {
        oldPath: oldFilePath,
        newPath: newPath,
        errorCode: errorCode
      }
    };
  }
});