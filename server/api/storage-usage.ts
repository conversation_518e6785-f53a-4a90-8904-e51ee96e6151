import { define<PERSON><PERSON><PERSON>and<PERSON> } from 'h3';
import * as fs from 'fs/promises';
import * as path from 'path';
import { createClient } from 'webdav';
import { LocalFileHandler } from '~/server/utils/localFileHandler';

interface Account {
  id: string;
  name: string;
  provider: 'webdav' | 'local';
  url?: string;
  username?: string;
  password?: string;
  localPath?: string;
  quotaLimit?: number;
  [key: string]: any;
}

interface StorageInfo {
  accountId: string;
  accountName: string;
  usedBytes: number;
  quotaLimitGB: number;
  isConnected: boolean;
  error?: string;
}

interface StorageResponse {
  accounts: StorageInfo[];
  totalUsedBytes: number;
  totalQuotaLimitGB: number;
  error?: string;
}

/**
 * Recursively calculates the total size of all files in a directory
 */
async function calculateDirectorySize(webdavClient: any, dirPath: string): Promise<number> {
  try {
    const items = await webdavClient.getDirectoryContents(dirPath);
    const itemsArray = Array.isArray(items) ? items : (items as any).data;
    
    let totalSize = 0;
    
    for (const item of itemsArray) {
      if (item.type === 'file') {
        totalSize += item.size || 0;
      } else if (item.type === 'directory') {
        // Recursively calculate subdirectory size
        const subDirSize = await calculateDirectorySize(webdavClient, item.filename);
        totalSize += subDirSize;
      }
    }
    
    return totalSize;
  } catch (error) {
    console.error(`Error calculating directory size for ${dirPath}:`, error);
    return 0;
  }
}

export default defineEventHandler(async (event): Promise<StorageResponse> => {
  try {
    const filePath = path.resolve(process.cwd(), 'data', 'accounts.json');
    const data = await fs.readFile(filePath, 'utf-8');
    const accounts: Account[] = JSON.parse(data);

    const storageInfoPromises = accounts.map(async (account): Promise<StorageInfo> => {
      const storageInfo: StorageInfo = {
        accountId: account.id,
        accountName: account.name,
        usedBytes: 0,
        quotaLimitGB: account.quotaLimit || 0,
        isConnected: false,
      };

      if (account.provider === 'webdav') {
        if (!account.url || !account.username || !account.password) {
          storageInfo.error = 'Missing WebDAV connection details';
          return storageInfo;
        }

        try {
          const webdavClient = createClient(account.url, {
            username: account.username,
            password: account.password,
          });

          // Test connection first
          await webdavClient.getDirectoryContents('/');
          storageInfo.isConnected = true;

          // Calculate total storage used
          storageInfo.usedBytes = await calculateDirectorySize(webdavClient, '/');
        } catch (error) {
          console.error(`Error checking WebDAV storage for account ${account.id}:`, error);
          storageInfo.error = `WebDAV connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
        }
      } else if (account.provider === 'local') {
        if (!account.localPath) {
          storageInfo.error = 'Missing local path';
          return storageInfo;
        }

        try {
          const localHandler = new LocalFileHandler(account.localPath);

          // Test if path exists and is accessible
          const exists = await localHandler.exists('/');
          storageInfo.isConnected = exists;

          if (exists) {
            // Calculate total storage used
            storageInfo.usedBytes = await localHandler.calculateDirectorySize('/');
          }
        } catch (error) {
          console.error(`Error checking local storage for account ${account.id}:`, error);
          storageInfo.error = `Local path access failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
        }
      } else {
        storageInfo.error = 'Invalid provider type';
        return storageInfo;
      }

      return storageInfo;
    });

    const storageResults = await Promise.all(storageInfoPromises);
    
    const totalUsedBytes = storageResults.reduce((sum, account) => sum + account.usedBytes, 0);
    const totalQuotaLimitGB = storageResults.reduce((sum, account) => sum + account.quotaLimitGB, 0);

    return {
      accounts: storageResults,
      totalUsedBytes,
      totalQuotaLimitGB,
    };
  } catch (error) {
    console.error('Error fetching storage usage:', error);
    return {
      accounts: [],
      totalUsedBytes: 0,
      totalQuotaLimitGB: 0,
      error: 'Failed to fetch storage usage',
    };
  }
});
