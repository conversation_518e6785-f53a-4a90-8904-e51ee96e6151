import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, getQuery } from 'h3';
import * as fs from 'fs/promises';
import * as path from 'path';
import { homedir, platform } from 'os';

interface DirectoryItem {
  name: string;
  path: string;
  type: 'directory' | 'drive';
  isAccessible: boolean;
  size?: string;
}

// Function to get available drives/volumes
async function getAvailableDrives(): Promise<DirectoryItem[]> {
  const drives: DirectoryItem[] = [];
  const isWindows = platform() === 'win32';

  if (isWindows) {
    // On Windows, check common drive letters
    const driveLetters = ['C:', 'D:', 'E:', 'F:', 'G:', 'H:', 'I:', 'J:', 'K:', 'L:', 'M:', 'N:', 'O:', 'P:', 'Q:', 'R:', 'S:', 'T:', 'U:', 'V:', 'W:', 'X:', 'Y:', 'Z:'];

    for (const letter of driveLetters) {
      const drivePath = letter + '\\';
      try {
        await fs.access(drivePath);
        const stats = await fs.stat(drivePath);
        if (stats.isDirectory()) {
          drives.push({
            name: `${letter} Drive`,
            path: drivePath,
            type: 'drive',
            isAccessible: true
          });
        }
      } catch {
        // Drive not available
      }
    }
  } else {
    // On Unix-like systems, show common mount points
    const commonPaths = [
      { name: 'Home Directory', path: homedir() },
      { name: 'Root (/)', path: '/' },
      { name: 'Users', path: '/Users' },
      { name: 'home', path: '/home' },
      { name: 'tmp', path: '/tmp' },
      { name: 'var', path: '/var' },
      { name: 'opt', path: '/opt' },
      { name: 'mnt', path: '/mnt' },
      { name: 'media', path: '/media' }
    ];

    for (const item of commonPaths) {
      try {
        await fs.access(item.path);
        const stats = await fs.stat(item.path);
        if (stats.isDirectory()) {
          drives.push({
            name: item.name,
            path: item.path,
            type: 'drive',
            isAccessible: true
          });
        }
      } catch {
        // Path not available
      }
    }
  }

  return drives;
}

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event);
    let currentPath = query.path as string;
    const showDrives = query.drives === 'true';

    // If requesting drives, return available drives/volumes
    if (showDrives) {
      const drives = await getAvailableDrives();
      return {
        currentPath: null,
        parentPath: null,
        directories: drives,
        totalDirectories: drives.length,
        isDriveView: true
      };
    }

    // Default to user's home directory if no path provided
    if (!currentPath) {
      currentPath = homedir();
    }

    // Security: Resolve the path to prevent path traversal
    const resolvedPath = path.resolve(currentPath);

    // Check if the path exists and is a directory
    try {
      const stats = await fs.stat(resolvedPath);
      if (!stats.isDirectory()) {
        return { 
          error: 'Path is not a directory',
          currentPath: resolvedPath,
          directories: []
        };
      }
    } catch (error) {
      return { 
        error: 'Path does not exist or is not accessible',
        currentPath: resolvedPath,
        directories: []
      };
    }

    // Read directory contents
    const entries = await fs.readdir(resolvedPath, { withFileTypes: true });
    
    // Filter only directories and check accessibility
    const directories: DirectoryItem[] = [];
    
    for (const entry of entries) {
      if (entry.isDirectory()) {
        const fullPath = path.join(resolvedPath, entry.name);
        let isAccessible = true;
        
        try {
          // Test if we can read the directory
          await fs.access(fullPath, fs.constants.R_OK);
        } catch {
          isAccessible = false;
        }
        
        directories.push({
          name: entry.name,
          path: fullPath,
          type: 'directory',
          isAccessible
        });
      }
    }

    // Sort directories alphabetically, with accessible ones first
    directories.sort((a, b) => {
      if (a.isAccessible !== b.isAccessible) {
        return a.isAccessible ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });

    // Get parent directory info
    const parentPath = path.dirname(resolvedPath);
    const canGoUp = parentPath !== resolvedPath; // Can't go up from root

    return {
      currentPath: resolvedPath,
      parentPath: canGoUp ? parentPath : null,
      directories,
      totalDirectories: directories.length,
      isDriveView: false
    };

  } catch (error) {
    console.error('Error browsing directories:', error);
    return {
      error: `Failed to browse directories: ${error instanceof Error ? error.message : 'Unknown error'}`,
      currentPath: homedir(),
      directories: [],
      isDriveView: false
    };
  }
});
