import { defineEvent<PERSON><PERSON><PERSON>, readBody } from 'h3';
import * as fs from 'fs/promises';
import * as path from 'path';

interface Account {
  id: string;
  name: string;
  provider: 'webdav' | 'local';
  url?: string; // Required for webdav, not used for local
  username?: string; // Required for webdav, not used for local
  password?: string; // Required for webdav, not used for local
  localPath?: string; // Required for local, not used for webdav
  email?: string;
  quotaLimit?: number;
  isConnected?: boolean;
  statusCode?: number;
  connectionError?: string;
}

// Simple ID generator
const generateId = () => {
  return 'acc-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
};

export default defineEventHandler(async (event) => {
  try {
    const filePath = path.resolve(process.cwd(), 'data', 'accounts.json');
    const data = await fs.readFile(filePath, 'utf-8');
    const accounts: Account[] = JSON.parse(data);

    const newAccount = await readBody(event);

    // Validate required fields based on provider type
    if (!newAccount.name || !newAccount.provider) {
      throw new Error('Name and provider type are required');
    }

    if (newAccount.provider === 'webdav') {
      if (!newAccount.url || !newAccount.username || !newAccount.password) {
        throw new Error('WebDAV accounts require URL, username, and password');
      }
    } else if (newAccount.provider === 'local') {
      if (!newAccount.localPath) {
        throw new Error('Local accounts require a local path');
      }
      // Validate that the path exists and is accessible
      try {
        const stats = await fs.stat(newAccount.localPath);
        if (!stats.isDirectory()) {
          throw new Error('Local path must be a directory');
        }
      } catch (error) {
        throw new Error(`Invalid local path: ${error instanceof Error ? error.message : 'Path not accessible'}`);
      }
    } else {
      throw new Error('Invalid provider type. Must be "webdav" or "local"');
    }

    // Create new account object
    const account: Account = {
      id: generateId(),
      name: newAccount.name,
      provider: newAccount.provider,
      isConnected: false, // Connection status will be checked on next GET request
    };

    // Add provider-specific fields
    if (newAccount.provider === 'webdav') {
      account.url = newAccount.url;
      account.username = newAccount.username;
      account.password = newAccount.password;
    } else if (newAccount.provider === 'local') {
      account.localPath = newAccount.localPath;
    }

    // Add optional fields
    if (newAccount.email) account.email = newAccount.email;
    if (newAccount.quotaLimit) account.quotaLimit = newAccount.quotaLimit;

    // Add to accounts array
    accounts.push(account);

    // Write updated accounts back to file
    await fs.writeFile(filePath, JSON.stringify(accounts, null, 2), 'utf-8');

    return {
      success: true,
      account: {
        id: account.id,
        name: account.name,
        provider: account.provider,
        url: account.url,
        localPath: account.localPath,
        isConnected: account.isConnected
      }
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to add account';
    console.error('Error adding account:', error);
    return {
      success: false,
      error: errorMessage
    };
  }
});