import { defineEventHand<PERSON>, readBody } from 'h3';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as XLSX from 'xlsx';
import { createClient } from 'webdav';

export default defineEventHandler(async (event) => {
  const accountId = event.context.params?.accountId;
  const filePath = event.context.params?.filePath;

  if (!accountId || !filePath) {
    return { error: 'Account ID and file path are required' };
  }

  try {
    const accountsFilePath = path.resolve(process.cwd(), 'data', 'accounts.json');
    const accountsData = await fs.readFile(accountsFilePath, 'utf-8');
    const accounts = JSON.parse(accountsData);

    const account = accounts.find((acc: any) => acc.id === accountId);

    if (!account) {
      return { error: 'Account not found' };
    }

    if (!account.url) {
      return { error: 'Account does not have a URL for file access' };
    }

    const fullFilePath = Array.isArray(filePath) ? filePath.join('/') : filePath;
    const body = await readBody(event);
    const { content, format, type } = body;

    if (!content || !format) {
      return { error: 'Content and format are required' };
    }

    let buffer: Buffer;
    const fileExtension = format.toLowerCase();

    // Convert content based on format
    switch (fileExtension) {
      case 'docx':
        buffer = await convertToDocx(content, type);
        break;
      case 'xlsx':
        buffer = await convertToXlsx(content, type);
        break;
      case 'csv':
        buffer = Buffer.from(convertToCsv(content, type), 'utf-8');
        break;
      case 'html':
        buffer = Buffer.from(convertToHtml(content, type), 'utf-8');
        break;
      case 'md':
        buffer = Buffer.from(convertToMarkdown(content, type), 'utf-8');
        break;
      case 'txt':
      default:
        buffer = Buffer.from(convertToText(content, type), 'utf-8');
        break;
    }

    // Use webdav client to upload the file
    const webdavClient = createClient(account.url, {
      username: account.name,
      password: account.password,
    });

    await webdavClient.putFileContents(fullFilePath, buffer, { overwrite: true });

    return { 
      message: 'Document saved successfully',
      filePath: fullFilePath,
      format: fileExtension
    };

  } catch (error) {
    console.error(`Error saving document for account ${accountId} at path ${filePath}:`, error);
    return { error: `Failed to save document: ${(error as Error).message}` };
  }
});

// Helper functions for format conversion
async function convertToDocx(content: any, type: string): Promise<Buffer> {
  const htmlDocx = await import('html-docx-js');
  
  let htmlContent = '';
  
  if (type === 'rich' && content.html) {
    htmlContent = content.html;
  } else if (content.text) {
    // Convert plain text to HTML with basic formatting
    htmlContent = content.text
      .split('\n')
      .map((line: string) => `<p>${line || '<br>'}</p>`)
      .join('');
  }

  // Wrap in a complete HTML document
  const fullHtml = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Document</title>
    </head>
    <body>
      ${htmlContent}
    </body>
    </html>
  `;

  return htmlDocx.asBlob(fullHtml);
}

async function convertToXlsx(content: any, type: string): Promise<Buffer> {
  if (type === 'spreadsheet' && content.workbook) {
    // Reconstruct workbook from serialized data
    const newWorkbook = XLSX.utils.book_new();
    for (const sheetName of content.workbook.SheetNames) {
      const sheetData = content.workbook.Sheets[sheetName];
      const ws: XLSX.WorkSheet = {};

      // Reconstruct the worksheet object from the serialized data
      if (sheetData.cells) {
        for (const cellRef in sheetData.cells) {
          ws[cellRef] = sheetData.cells[cellRef];
        }
      }
      ws['!ref'] = sheetData['!ref'];
      ws['!merges'] = sheetData['!merges'];

      XLSX.utils.book_append_sheet(newWorkbook, ws, sheetName);
    }

    return XLSX.write(newWorkbook, { type: 'buffer', bookType: 'xlsx' });
  } else {
    // Convert text content to simple spreadsheet
    const lines = (content.text || '').split('\n');
    const data = lines.map((line: string) => [line]);
    
    const ws = XLSX.utils.aoa_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    
    return XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });
  }
}

function convertToCsv(content: any, type: string): string {
  if (type === 'spreadsheet' && content.workbook) {
    // Convert first sheet to CSV
    const firstSheetName = content.workbook.SheetNames[0];
    const worksheet = content.workbook.Sheets[firstSheetName];
    return XLSX.utils.sheet_to_csv(worksheet);
  } else {
    // Convert text to simple CSV (one column)
    const lines = (content.text || '').split('\n');
    return lines.map((line: string) => `"${line.replace(/"/g, '""')}"`).join('\n');
  }
}

function convertToHtml(content: any, type: string): string {
  if (type === 'rich' && content.html) {
    return `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Document</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
  </style>
</head>
<body>
  ${content.html}
</body>
</html>`;
  } else {
    // Convert plain text to HTML
    const htmlContent = (content.text || '')
      .split('\n')
      .map((line: string) => `<p>${line || '<br>'}</p>`)
      .join('');
    
    return `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Document</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
  </style>
</head>
<body>
  ${htmlContent}
</body>
</html>`;
  }
}

function convertToMarkdown(content: any, type: string): string {
  if (type === 'rich' && content.html) {
    // Convert HTML to Markdown using turndown
    const TurndownService = require('turndown');
    const turndownService = new TurndownService();
    return turndownService.turndown(content.html);
  } else {
    return content.text || '';
  }
}

function convertToText(content: any, type: string): string {
  return content.text || '';
}
