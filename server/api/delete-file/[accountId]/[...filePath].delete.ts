import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3';
import * as fs from 'fs/promises';
import * as path from 'path';
import { createClient } from 'webdav';

export default defineEventHandler(async (event) => {
  const accountId = event.context.params?.accountId;
  const filePath = event.context.params?.filePath;

  if (!accountId) {
    return { error: 'Account ID is required' };
  }
  if (!filePath) {
    return { error: 'File path is required' };
  }

  try {
    const accountsFilePath = path.resolve(process.cwd(), 'data', 'accounts.json');
    const accountsData = await fs.readFile(accountsFilePath, 'utf-8');
    const accounts = JSON.parse(accountsData);

    const account = accounts.find((acc: any) => acc.id === accountId);

    if (!account) {
      return { error: 'Account not found' };
    }

    if (!account.url) {
      return { error: 'Account does not have a URL for file access' };
    }

    const webdavClient = createClient(
      account.url,
      {
        username: account.name,
        password: account.password,
      }
    );

    await webdavClient.deleteFile(filePath);

    return { message: `File ${filePath} deleted successfully.` };
  } catch (error) {
    console.error(`Error deleting file for account ${accountId} and path ${filePath}:`, error);
    return { error: `Failed to delete file: ${(error as Error).message}` };
  }
});