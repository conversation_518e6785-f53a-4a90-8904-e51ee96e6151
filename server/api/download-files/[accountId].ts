import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, getQ<PERSON>y } from 'h3';
import archiver from 'archiver';
import * as fs from 'fs/promises';
import * as path from 'path';
import { createClient } from 'webdav';

export default defineEventHandler(async (event) => {
  const accountId = event.context.params?.accountId;
  const query = getQuery(event);
  const filePaths = query.paths ? (Array.isArray(query.paths) ? query.paths : [query.paths]) : [];

  if (!accountId || filePaths.length === 0) {
    event.node.res.statusCode = 400;
    return { error: 'Account ID and file paths are required.' };
  }

  let account;
  try {
    const accountsFilePath = path.resolve(process.cwd(), 'data', 'accounts.json');
    const accountsData = await fs.readFile(accountsFilePath, 'utf-8');
    const accounts = JSON.parse(accountsData);
    account = accounts.find((acc: any) => acc.id === accountId);

    if (!account) {
      event.node.res.statusCode = 404;
      return { error: 'Account not found.' };
    }
    if (!account.url) {
      event.node.res.statusCode = 400;
      return { error: 'Account does not have a URL for file access.' };
    }
  } catch (e: any) {
    console.error('Error reading accounts.json:', e);
    event.node.res.statusCode = 500;
    return { error: `Server error: ${e.message}` };
  }

  const webdavClient = createClient(
    account.url,
    {
      username: account.name,
      password: account.password,
    }
  );

  event.node.res.setHeader('Content-Type', 'application/zip');
  event.node.res.setHeader('Content-Disposition', `attachment; filename="selected_files_${accountId}.zip"`);

  const archive = archiver('zip', {
    zlib: { level: 9 }
  });

  archive.on('error', (err: any) => {
    console.error('Archive error:', err);
    if (!event.node.res.headersSent) {
      event.node.res.statusCode = 500;
      event.node.res.end(JSON.stringify({ error: 'Failed to create zip archive.' }));
    }
  });

  archive.pipe(event.node.res);

  for (const filePath of filePaths) {
    try {
      const fileStream = webdavClient.createReadStream(filePath);
      archive.append(fileStream, { name: path.basename(filePath) });
    } catch (e: any) {
      console.warn(`Error adding file ${filePath} to archive: ${e.message}`);
      // Continue to next file even if one fails
    }
  }

  await archive.finalize();
});