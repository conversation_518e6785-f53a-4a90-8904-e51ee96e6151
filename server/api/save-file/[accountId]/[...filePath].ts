import { defineEvent<PERSON>and<PERSON>, readBody } from 'h3';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as XLSX from 'xlsx';
import { createClient } from 'webdav';

export default defineEventHandler(async (event) => {
  const accountId = event.context.params?.accountId;
  const filePath = event.context.params?.filePath;

  if (!accountId || !filePath) {
    return { error: 'Account ID and file path are required' };
  }

  try {
    const accountsFilePath = path.resolve(process.cwd(), 'data', 'accounts.json');
    const accountsData = await fs.readFile(accountsFilePath, 'utf-8');
    const accounts = JSON.parse(accountsData);

    const account = accounts.find((acc: any) => acc.id === accountId);

    if (!account) {
      return { error: 'Account not found' };
    }

    if (!account.url) {
      return { error: 'Account does not have a URL for file access' };
    }

    const fullFilePath = Array.isArray(filePath) ? filePath.join('/') : filePath;
    const fileUrl = `${account.url}${fullFilePath}`;

    const body = await readBody(event);
    const { workbook } = body; // Expecting full workbook object

    if (!workbook || typeof workbook !== 'object') {
      return { error: 'Invalid data format. Expected a workbook object.' };
    }

    // Reconstruct workbook from serialized data
    const newWorkbook = XLSX.utils.book_new();
    for (const sheetName of workbook.SheetNames) {
      const sheetData = workbook.Sheets[sheetName];
      const ws: XLSX.WorkSheet = {};

      // Reconstruct the worksheet object from the serialized data
      for (const cellRef in sheetData.cells) {
        ws[cellRef] = sheetData.cells[cellRef];
      }
      ws['!ref'] = sheetData['!ref'];
      ws['!merges'] = sheetData['!merges'];

      XLSX.utils.book_append_sheet(newWorkbook, ws, sheetName);
    }

    // Write the workbook to a buffer
    const buffer = XLSX.write(newWorkbook, { type: 'buffer', bookType: 'xlsx' });

    // Use webdav client to upload the file
    const webdavClient = createClient(account.url, {
      username: account.name,
      password: account.password,
    });

    await webdavClient.putFileContents(fullFilePath, buffer, { overwrite: true });

    return { message: 'File saved successfully' };

  } catch (error) {
    console.error(`Error saving file content for account ${accountId} at path ${filePath}:`, error);
    return { error: `Failed to save file content: ${(error as Error).message}` };
  }
});