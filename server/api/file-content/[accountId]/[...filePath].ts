import { defineEventHand<PERSON>, setHeaders, send, getRequestHeaders } from 'h3';
import * as fs from 'fs/promises';
import * as path from 'path';
import { createClient } from 'webdav';
import { createHash } from 'crypto';

const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
const videoExtensions = ['.mp4', '.webm', '.ogg'];

export default defineEventHandler(async (event) => {
  const accountId = event.context.params?.accountId;
  const filePath = event.context.params?.filePath;

  if (!accountId) {
    event.node.res.statusCode = 400;
    return { error: 'Account ID is required' };
  }
  if (!filePath) {
    event.node.res.statusCode = 400;
    return { error: 'File path is required' };
  }

  try {
    const accountsFilePath = path.resolve(process.cwd(), 'data', 'accounts.json');
    const accountsData = await fs.readFile(accountsFilePath, 'utf-8');
    const accounts = JSON.parse(accountsData);

    const account = accounts.find((acc: any) => acc.id === accountId);

    if (!account) {
      event.node.res.statusCode = 404;
      return { error: 'Account not found' };
    }

    if (!account.url) {
      event.node.res.statusCode = 500;
      return { error: 'Account does not have a URL for file access' };
    }

    const webdavClient = createClient(
      account.url,
      {
        username: account.name,
        password: account.password,
      }
    );

    const fileExtension = path.extname(filePath).toLowerCase();
    let format: 'text' | 'binary' = 'text';
    let contentType = 'application/octet-stream'; // Default content type
    let isBinaryContent = false;

    if (imageExtensions.includes(fileExtension)) {
      format = 'binary';
      contentType = `image/${fileExtension.substring(1)}`;
      if (fileExtension === '.jpg') contentType = 'image/jpeg';
      isBinaryContent = true;
    } else if (videoExtensions.includes(fileExtension)) {
      format = 'binary';
      contentType = `video/${fileExtension.substring(1)}`;
      isBinaryContent = true;
    } else if (fileExtension === '.pdf') {
      contentType = 'application/pdf';
      format = 'binary';
      isBinaryContent = true;
    } else if (fileExtension === '.txt') {
      contentType = 'text/plain';
    } else if (fileExtension === '.json') {
      contentType = 'application/json';
    } else if (fileExtension === '.xml') {
      contentType = 'application/xml';
    } else if (fileExtension === '.html' || fileExtension === '.htm') {
      contentType = 'text/html';
    } else if (fileExtension === '.css') {
      contentType = 'text/css';
    } else if (fileExtension === '.js') {
      contentType = 'application/javascript';
    }

    // Decode filePath before passing to webdavClient
    const decodedFilePath = decodeURIComponent(filePath);
    console.log(`[DEBUG] Attempting to fetch file content for decoded path: ${decodedFilePath} with format: ${format}`);
    const fileContent = await webdavClient.getFileContents(decodedFilePath, { format });

    // Log file content for text files to verify changes
    if (format === 'text') {
      console.log(`[DEBUG] Fetched file content for ${decodedFilePath}:`, fileContent);
    }

    const etag = createHash('sha256').update(fileContent).digest('hex');
    console.log(`[DEBUG] Generated ETag for ${decodedFilePath}: ${etag}`);
    const requestHeaders = getRequestHeaders(event);
    const ifNoneMatch = requestHeaders['if-none-match'];

    setHeaders(event, {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'ETag': etag,
      'Content-Disposition': `inline; filename="${path.basename(decodedFilePath)}"`
    });

    if (ifNoneMatch === etag) {
      event.node.res.statusCode = 304; // Not Modified
      return '';
    }

    if (isBinaryContent) {
      return send(event, fileContent, contentType);
    } else {
      setHeaders(event, { 'Content-Type': contentType });
      return fileContent;
    }
  } catch (error) {
    console.error(`Error fetching file content for account ${accountId} and path ${filePath}:`, error);
    console.error('Detailed error:', error); // Log the full error object
    event.node.res.statusCode = 500;
    return { error: `Failed to fetch file content: ${(error as Error).message}` };
  }
});
