import { defineEventH<PERSON><PERSON>, readMultipartFormData } from 'h3';
import * as fs from 'fs/promises';
import * as path from 'path';
import { createClient } from 'webdav';
import { LocalFileHandler } from '~/server/utils/localFileHandler';
import { fileCache } from '../files'; // Import the fileCache

export default defineEventHandler(async (event) => {
  const accountId = event.context.params?.accountId;

  if (!accountId) {
    return { error: 'Account ID is required' };
  }

  try {
    const accountsFilePath = path.resolve(process.cwd(), 'data', 'accounts.json');
    const accountsData = await fs.readFile(accountsFilePath, 'utf-8');
    const accounts = JSON.parse(accountsData);

    const account = accounts.find((acc: any) => acc.id === accountId);

    if (!account) {
      return { error: 'Account not found' };
    }

    if (account.provider === 'webdav' && !account.url) {
      return { error: 'WebDAV account does not have a URL for file access' };
    }

    if (account.provider === 'local' && !account.localPath) {
      return { error: 'Local account does not have a local path configured' };
    }

    const formData = await readMultipartFormData(event);
    if (!formData) {
      return { error: 'No form data received' };
    }

    let file: any;
    let uploadPath = '/'; // default to root
    for (const part of formData) {
      if (part.name === 'file') {
        file = part;
      } else if (part.name === 'path') {
        uploadPath = part.data.toString('utf-8');
      }
    }

    if (!file) {
      return { error: 'File is required' };
    }

    // Construct full file path
    let fullFilePath = path.posix.join(uploadPath, file.filename);
    // Ensure the path always starts with a single '/'
    if (!fullFilePath.startsWith('/')) {
      fullFilePath = '/' + fullFilePath;
    }
    // Remove any double slashes that might occur from path concatenation
    fullFilePath = fullFilePath.replace(/\/\/+/g, '/');

    if (account.provider === 'local') {
      try {
        const localHandler = new LocalFileHandler(account.localPath!);
        await localHandler.putFileContents(fullFilePath, file.data);

        return {
          success: true,
          message: `File ${file.filename} uploaded successfully to local storage`,
          path: fullFilePath
        };
      } catch (error) {
        console.error('Error uploading file to local storage:', error);
        return { error: `Failed to upload file to local storage: ${error instanceof Error ? error.message : 'Unknown error'}` };
      }
    } else if (account.provider === 'webdav') {
      try {
        const webdavClient = createClient(account.url!, {
          username: account.username || account.name,
          password: account.password,
        });

        await webdavClient.putFileContents(fullFilePath, file.data, { overwrite: true });

        // Invalidate the cache for the directory where the file was uploaded
        const cacheKeyToInvalidate = `${accountId}-${uploadPath}`;
        fileCache.delete(cacheKeyToInvalidate);

        console.log(`File uploaded successfully: ${fullFilePath} to account ${accountId}`);
        return {
          success: true,
          message: 'File uploaded successfully to WebDAV',
          filePath: fullFilePath,
          accountId: accountId
        };
      } catch (error) {
        console.error('Error uploading file to WebDAV:', error);
        return { error: `Failed to upload file to WebDAV: ${(error as Error).message}` };
      }
    } else {
      return { error: 'Invalid account provider type' };
    }

  } catch (error) {
    console.error(`Error uploading file for account ${accountId}:`, error);
    return { error: `Failed to upload file: ${(error as Error).message}` };
  }
});