#!/bin/bash

# InfiniCloud Manager - Production Setup Script
# This script sets up the entire application with SSL on a new server

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Load configuration
if [ -f "config.env" ]; then
    source config.env
    echo -e "${GREEN}✅ Loaded configuration from config.env${NC}"
else
    echo -e "${YELLOW}⚠️  config.env not found, using defaults${NC}"
    DOMAIN="webmirai.duckdns.org"
    EMAIL="<EMAIL>"
    APP_NAME="infinicloud-manager"
fi

echo -e "${BLUE}🚀 InfiniCloud Manager Production Setup${NC}"
echo -e "${BLUE}=======================================${NC}"
echo ""
echo -e "${YELLOW}Domain: ${DOMAIN}${NC}"
echo -e "${YELLOW}Email: ${EMAIL}${NC}"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install Docker
install_docker() {
    echo -e "${BLUE}📦 Installing Docker...${NC}"
    
    # Update package index
    sudo apt-get update
    
    # Install prerequisites
    sudo apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release
    
    # Add Docker's official GPG key
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # Add Docker repository
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Install Docker
    sudo apt-get update
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io
    
    # Add current user to docker group
    sudo usermod -aG docker $USER
    
    echo -e "${GREEN}✅ Docker installed successfully${NC}"
}

# Function to install Docker Compose
install_docker_compose() {
    echo -e "${BLUE}📦 Installing Docker Compose...${NC}"
    
    # Download and install Docker Compose
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    echo -e "${GREEN}✅ Docker Compose installed successfully${NC}"
}

# Check prerequisites
echo -e "${BLUE}🔍 Checking prerequisites...${NC}"

if ! command_exists docker; then
    install_docker
    echo -e "${YELLOW}⚠️  Please log out and log back in for Docker permissions to take effect${NC}"
    echo -e "${YELLOW}⚠️  Then run this script again${NC}"
    exit 1
fi

if ! command_exists docker-compose; then
    install_docker_compose
fi

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo -e "${YELLOW}🔄 Starting Docker service...${NC}"
    sudo systemctl start docker
    sudo systemctl enable docker
fi

echo -e "${GREEN}✅ Prerequisites check complete${NC}"
echo ""

# Create necessary directories
echo -e "${BLUE}📁 Creating directories...${NC}"
mkdir -p scripts
mkdir -p data

echo -e "${GREEN}✅ Directories created${NC}"
echo ""

# Build and start the application
echo -e "${BLUE}🔨 Building application...${NC}"
docker-compose build

echo -e "${BLUE}▶️  Starting application (HTTP only for SSL setup)...${NC}"
docker-compose up -d

# Wait for application to start
echo -e "${BLUE}⏳ Waiting for application to start...${NC}"
sleep 15

# Check if application is running
if ! curl -f -s http://localhost >/dev/null; then
    echo -e "${RED}❌ Application failed to start${NC}"
    echo -e "${YELLOW}📋 Check logs with: docker-compose logs${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Application started successfully${NC}"
echo ""

# Setup SSL
echo -e "${BLUE}🔐 Setting up SSL certificates...${NC}"

# Get SSL certificate
echo -e "${BLUE}📜 Obtaining SSL certificate from Let's Encrypt...${NC}"
docker-compose run --rm certbot certonly --webroot --webroot-path=/var/www/certbot \
  --email "$EMAIL" --agree-tos --no-eff-email -d "$DOMAIN" \
  --non-interactive

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ SSL certificate obtained successfully${NC}"
else
    echo -e "${RED}❌ Failed to obtain SSL certificate${NC}"
    echo -e "${YELLOW}⚠️  Make sure your domain points to this server's IP${NC}"
    echo -e "${YELLOW}⚠️  Current server IP: $(curl -s ifconfig.me)${NC}"
    exit 1
fi

# Reload nginx with SSL configuration
echo -e "${BLUE}🔄 Reloading nginx with SSL...${NC}"
docker exec infinicloud-nginx nginx -s reload

echo ""
echo -e "${GREEN}🎉 Setup Complete!${NC}"
echo -e "${GREEN}==================${NC}"
echo ""
echo -e "${GREEN}✅ Application is running with SSL${NC}"
echo -e "${GREEN}🌐 Access your app at: https://${DOMAIN}${NC}"
echo ""
echo -e "${BLUE}📋 Useful commands:${NC}"
echo -e "${YELLOW}  View logs:${NC} docker-compose logs"
echo -e "${YELLOW}  Restart:${NC} docker-compose restart"
echo -e "${YELLOW}  Stop:${NC} docker-compose down"
echo -e "${YELLOW}  Renew SSL:${NC} ./scripts/renew-ssl.sh"
echo ""
echo -e "${BLUE}📅 SSL Certificate expires in 90 days${NC}"
echo -e "${BLUE}💡 Set up a cron job to run ./scripts/renew-ssl.sh monthly${NC}"
