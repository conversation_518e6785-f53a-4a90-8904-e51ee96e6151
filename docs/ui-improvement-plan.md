# UI Improvement Plan for File Manager Dashboard

## Project Overview
This document outlines the plan to improve the UI of the file manager dashboard (`pages/index.vue`) and create a reusable component architecture for AI-assisted development.

## Current UI Analysis
- **Strengths**: Clear information hierarchy, responsive grid layout, visual status indicators
- **Areas for Improvement**: 
  - Limited component reusability
  - Minimal interactivity features
  - Monolithic file structure
  - Inconsistent styling approach

## Proposed Component Architecture

```mermaid
graph TD
    A[IndexPage] --> B[HeaderComponent]
    A --> C[StatsSummary]
    A --> D[StorageVisualization]
    A --> E[AccountGrid]
    E --> F[AccountCard]
    F --> G[StatusIndicator]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#ccf,stroke:#333
    style C fill:#ccf,stroke:#333
    style D fill:#ccf,stroke:#333
    style E fill:#ccf,stroke:#333
    style F fill:#cfc,stroke:#333
    style G fill:#fcc,stroke:#333
```

## Implementation Roadmap

### Phase 1: Component Creation
1. Create component directory structure:
   ```bash
   components/
     dashboard/
       HeaderComponent.vue
       StatsSummary.vue
       StorageVisualization.vue
       AccountGrid.vue
       AccountCard.vue
       StatusIndicator.vue
   ```

2. Implement base components with props:
   ```vue
   <!-- AccountCard.vue -->
   <template>
     <div class="account-card">
       <slot name="icon">📁</slot>
       <h3>{{ title }}</h3>
       <StatusIndicator :status="status" />
       <slot></slot>
     </div>
   </template>
   ```

### Phase 2: Index Page Refactor
1. Replace existing markup with new component structure:
   ```vue
   <template>
     <HeaderComponent title="File Manager Dashboard" />
     <StatsSummary :stats="accountStats" />
     <StorageVisualization :usage="storageUsage" />
     <AccountGrid :accounts="accounts" />
   </template>
   ```

2. Implement state management between components

### Phase 3: Enhanced Features
1. Add account filtering:
   ```vue
   <AccountGrid :accounts="accounts" filter-by="connected" />
   ```

2. Implement storage breakdown visualization:
   ```vue
   <StorageVisualization 
     :usage="storageUsage" 
     show-breakdown 
     :accounts="accounts"
   />
   ```

## Component Documentation Guidelines
Each component file should include:

```vue
/**
 * @name AccountCard
 * @description Reusable account card component
 * @prop {String} title - Account name
 * @prop {Boolean} status - Connection status
 * @slot icon - Custom icon content
 * @slot default - Additional account info
 * @event click - Emitted when card is clicked
 */
```

## Next Steps
1. Switch to Code mode for implementation - **Implemented**
2. Create component files with base templates - **Implemented**
3. Refactor index page to use new components - **Implemented**
4. Apply UI improvements to components - **Implemented**
5. Update documentation - **Implemented**