<template>
  <div>
    <HeaderComponent title="File Manager Dashboard" @toggle-sidebar="toggleSidebar" />
    <Sidebar :is-open="isSidebarOpen" @close="toggleSidebar" />
    <div class="overlay" :class="{ 'is-visible': isSidebarOpen }" @click="toggleSidebar"></div>
    <main class="main-content">
      <slot />
    </main>
    <footer>
      <p>&copy; {{ new Date().getFullYear() }} Chinen Cloud</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import HeaderComponent from '~/components/dashboard/HeaderComponent.vue';
import Sidebar from '~/components/Sidebar.vue';

const isSidebarOpen = ref(false);

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value;
};
</script>

<style scoped>

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999; 
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.overlay.is-visible {
  opacity: 1;
  visibility: visible;
}

footer {
  text-align: center;
  padding: 20px;
  margin-top: 40px;
  border-top: 1px solid #e2e8f0;
  color: #718096;
  font-size: 0.9em;
}
</style>