<template>
  <div v-if="show" class="modal-overlay" @click.self="$emit('close')">
    <div class="modal-content">
      <!-- Header with title and main controls -->
      <div class="modal-header">
        <div class="header-left">
          <h2 class="file-title">📊 {{ fileName }}</h2>
          <div class="file-info">
            <span class="sheet-count">{{ workbook?.SheetNames?.length || 0 }} sheets</span>
            <span class="modified-indicator" v-if="hasUnsavedChanges">● Modified</span>
          </div>
        </div>
        <div class="header-right">
          <button class="header-btn" @click="downloadFile" title="Download">
            <span class="icon">💾</span>
          </button>
          <button class="header-btn" @click="printSheet" title="Print">
            <span class="icon">🖨️</span>
          </button>
          <button class="header-btn save-btn" @click="saveChanges" :disabled="!hasUnsavedChanges">
            <span class="icon">💾</span>
            Save
          </button>
          <button class="modal-close" @click="$emit('close')" title="Close">×</button>
        </div>
      </div>

      <div v-if="loading" class="loading-indicator">
        <div class="spinner"></div>
        Loading spreadsheet data...
      </div>

      <div v-else-if="error" class="error-message">
        <span class="error-icon">⚠️</span>
        Error: {{ error }}
        <button @click="fetchXlsxData" class="retry-btn">Retry</button>
      </div>

      <div v-else class="spreadsheet-container">
        <!-- Toolbar -->
        <div class="toolbar">
          <div class="toolbar-section">
            <label class="toolbar-label">Format:</label>
            <select v-model="selectedFormat" @change="applyFormat" class="format-select">
              <option value="general">General</option>
              <option value="number">Number</option>
              <option value="currency">Currency</option>
              <option value="percentage">Percentage</option>
              <option value="date">Date</option>
              <option value="text">Text</option>
            </select>
          </div>

          <div class="toolbar-section">
            <button @click="toggleBold" :class="{ active: isBold }" class="format-btn" title="Bold">
              <strong>B</strong>
            </button>
            <button @click="toggleItalic" :class="{ active: isItalic }" class="format-btn" title="Italic">
              <em>I</em>
            </button>
            <button @click="toggleUnderline" :class="{ active: isUnderline }" class="format-btn" title="Underline">
              <u>U</u>
            </button>
          </div>

          <div class="toolbar-section">
            <button @click="setAlignment('left')" :class="{ active: alignment === 'left' }" class="format-btn" title="Align Left">
              ⬅️
            </button>
            <button @click="setAlignment('center')" :class="{ active: alignment === 'center' }" class="format-btn" title="Center">
              ↔️
            </button>
            <button @click="setAlignment('right')" :class="{ active: alignment === 'right' }" class="format-btn" title="Align Right">
              ➡️
            </button>
          </div>

          <div class="toolbar-section">
            <input type="color" v-model="textColor" @change="applyTextColor" class="color-picker" title="Text Color">
            <input type="color" v-model="backgroundColor" @change="applyBackgroundColor" class="color-picker" title="Background Color">
          </div>

          <div class="toolbar-section">
            <button @click="insertRow" class="action-btn" title="Insert Row">
              ➕ Row
            </button>
            <button @click="insertColumn" class="action-btn" title="Insert Column">
              ➕ Col
            </button>
            <button @click="deleteRow" class="action-btn danger" title="Delete Row">
              ➖ Row
            </button>
            <button @click="deleteColumn" class="action-btn danger" title="Delete Column">
              ➖ Col
            </button>
          </div>

          <div class="toolbar-section">
            <button @click="mergeCells" class="action-btn" title="Merge Cells">
              🔗 Merge
            </button>
            <button @click="unmergeCells" class="action-btn" title="Unmerge Cells">
              🔓 Unmerge
            </button>
          </div>

          <div class="toolbar-section">
            <button @click="undo" :disabled="!canUndo" class="action-btn" title="Undo">
              ↶ Undo
            </button>
            <button @click="redo" :disabled="!canRedo" class="action-btn" title="Redo">
              ↷ Redo
            </button>
          </div>
        </div>

        <!-- Sheet tabs -->
        <div class="sheet-tabs">
          <button
            v-for="(sheet, index) in workbook.SheetNames"
            :key="index"
            :class="{ active: activeSheetIndex === index }"
            @click="activeSheetIndex = index"
            @contextmenu.prevent="showSheetContextMenu($event, index)"
            class="sheet-tab"
          >
            {{ sheet }}
          </button>
          <button @click="addNewSheet" class="add-sheet-btn" title="Add Sheet">
            ➕
          </button>
        </div>

        <!-- Formula bar -->
        <div class="formula-bar">
          <div class="cell-reference">
            <input v-model="selectedCellRef" @keyup.enter="goToCell" class="cell-ref-input" placeholder="A1">
          </div>
          <div class="formula-input-container">
            <span class="formula-prefix">fx</span>
            <input
              v-model="formulaText"
              @keyup.enter="applyFormula"
              @focus="isEditingFormula = true"
              @blur="isEditingFormula = false"
              class="formula-input"
              placeholder="Enter formula or value"
            >
          </div>
        </div>

        <div class="spreadsheet-wrapper">
          <div class="spreadsheet-grid" ref="grid" @click="handleCellClick" @keydown="handleKeyDown"></div>
        </div>
      </div>

      <!-- Context menu for sheets -->
      <div v-if="showContextMenu" class="context-menu" :style="contextMenuStyle">
        <button @click="renameSheet" class="context-item">Rename Sheet</button>
        <button @click="duplicateSheet" class="context-item">Duplicate Sheet</button>
        <button @click="deleteSheet" class="context-item danger" :disabled="workbook?.SheetNames?.length <= 1">Delete Sheet</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, nextTick, computed } from 'vue';
import * as XLSX from 'xlsx';

const props = defineProps({
  accountId: {
    type: String,
    required: true
  },
  filePath: {
    type: String,
    required: true
  },
  show: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'saved']);

// Basic state
const loading = ref(false);
const error = ref(null);
const fileName = ref('');
const workbook = ref(null);
const activeSheetIndex = ref(0);
const grid = ref(null);

// New state for enhanced features
const hasUnsavedChanges = ref(false);
const selectedCellRef = ref('A1');
const formulaText = ref('');
const isEditingFormula = ref(false);

// Formatting state
const selectedFormat = ref('general');
const isBold = ref(false);
const isItalic = ref(false);
const isUnderline = ref(false);
const alignment = ref('left');
const textColor = ref('#000000');
const backgroundColor = ref('#ffffff');

// Context menu state
const showContextMenu = ref(false);
const contextMenuStyle = ref({});
const contextMenuSheetIndex = ref(0);

// Undo/Redo state
const undoStack = ref([]);
const redoStack = ref([]);
const canUndo = computed(() => undoStack.value.length > 0);
const canRedo = computed(() => redoStack.value.length > 0);

// Utility functions
const saveState = () => {
  if (workbook.value) {
    undoStack.value.push(JSON.parse(JSON.stringify(workbook.value)));
    redoStack.value = []; // Clear redo stack when new action is performed
    if (undoStack.value.length > 50) { // Limit undo stack size
      undoStack.value.shift();
    }
  }
};

const markAsModified = () => {
  hasUnsavedChanges.value = true;
};

const fetchXlsxData = async () => {
  if (!props.show || !props.accountId || !props.filePath) return;

  loading.value = true;
  error.value = null;
  fileName.value = props.filePath.split('/').pop();

  try {
    const response = await fetch(`/api/file-content/${props.accountId}/${props.filePath}?format=workbook`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    workbook.value = await response.json();
    hasUnsavedChanges.value = false;
    undoStack.value = [];
    redoStack.value = [];
  } catch (e) {
    error.value = e.message;
    console.error("Error fetching XLSX data:", e);
  } finally {
    loading.value = false;
  }
};

// Formatting methods
const toggleBold = () => {
  isBold.value = !isBold.value;
  applyFormatToSelectedCells({ bold: isBold.value });
};

const toggleItalic = () => {
  isItalic.value = !isItalic.value;
  applyFormatToSelectedCells({ italic: isItalic.value });
};

const toggleUnderline = () => {
  isUnderline.value = !isUnderline.value;
  applyFormatToSelectedCells({ underline: isUnderline.value });
};

const setAlignment = (align) => {
  alignment.value = align;
  applyFormatToSelectedCells({ hAlign: align });
};

const applyFormat = () => {
  applyFormatToSelectedCells({ format: selectedFormat.value });
};

const applyTextColor = () => {
  applyFormatToSelectedCells({ fontColor: textColor.value.substring(1) });
};

const applyBackgroundColor = () => {
  applyFormatToSelectedCells({ bgColor: backgroundColor.value.substring(1) });
};

const applyFormatToSelectedCells = (formatOptions) => {
  if (!workbook.value || !selectedCellRef.value) return;

  saveState();
  const ws = workbook.value.Sheets[workbook.value.SheetNames[activeSheetIndex.value]];
  const cell = ws[selectedCellRef.value];

  if (cell) {
    if (!cell.s) cell.s = {};
    Object.assign(cell.s, formatOptions);
    markAsModified();
    renderSpreadsheet();
  }
};

// Cell and sheet manipulation methods
const insertRow = () => {
  if (!workbook.value) return;
  saveState();
  // Implementation for inserting row
  markAsModified();
  renderSpreadsheet();
};

const insertColumn = () => {
  if (!workbook.value) return;
  saveState();
  // Implementation for inserting column
  markAsModified();
  renderSpreadsheet();
};

const deleteRow = () => {
  if (!workbook.value) return;
  saveState();
  // Implementation for deleting row
  markAsModified();
  renderSpreadsheet();
};

const deleteColumn = () => {
  if (!workbook.value) return;
  saveState();
  // Implementation for deleting column
  markAsModified();
  renderSpreadsheet();
};

const mergeCells = () => {
  if (!workbook.value) return;
  saveState();
  // Implementation for merging cells
  markAsModified();
  renderSpreadsheet();
};

const unmergeCells = () => {
  if (!workbook.value) return;
  saveState();
  // Implementation for unmerging cells
  markAsModified();
  renderSpreadsheet();
};

// Undo/Redo functionality
const undo = () => {
  if (undoStack.value.length > 0) {
    redoStack.value.push(JSON.parse(JSON.stringify(workbook.value)));
    workbook.value = undoStack.value.pop();
    renderSpreadsheet();
  }
};

const redo = () => {
  if (redoStack.value.length > 0) {
    undoStack.value.push(JSON.parse(JSON.stringify(workbook.value)));
    workbook.value = redoStack.value.pop();
    renderSpreadsheet();
  }
};

// Formula and navigation methods
const applyFormula = () => {
  if (!workbook.value || !selectedCellRef.value || !formulaText.value) return;

  saveState();
  const ws = workbook.value.Sheets[workbook.value.SheetNames[activeSheetIndex.value]];

  if (!ws[selectedCellRef.value]) {
    ws[selectedCellRef.value] = {};
  }

  const cell = ws[selectedCellRef.value];
  cell.f = formulaText.value.startsWith('=') ? formulaText.value.substring(1) : formulaText.value;
  cell.v = formulaText.value;
  cell.w = formulaText.value;

  markAsModified();
  renderSpreadsheet();
};

const goToCell = () => {
  if (selectedCellRef.value) {
    // Focus on the specified cell
    const cellElement = grid.value?.querySelector(`[data-cell="${selectedCellRef.value}"]`);
    if (cellElement) {
      cellElement.focus();
      cellElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }
};

const handleCellClick = (event) => {
  const cellRef = event.target.getAttribute('data-cell');
  if (cellRef) {
    selectedCellRef.value = cellRef;
    updateFormulaBar(cellRef);
    updateFormattingState(cellRef);
  }
};

const handleKeyDown = (event) => {
  // Handle keyboard shortcuts
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'z':
        event.preventDefault();
        if (event.shiftKey) {
          redo();
        } else {
          undo();
        }
        break;
      case 's':
        event.preventDefault();
        saveChanges();
        break;
      case 'b':
        event.preventDefault();
        toggleBold();
        break;
      case 'i':
        event.preventDefault();
        toggleItalic();
        break;
    }
  }
};

const updateFormulaBar = (cellRef) => {
  if (!workbook.value) return;

  const ws = workbook.value.Sheets[workbook.value.SheetNames[activeSheetIndex.value]];
  const cell = ws[cellRef];

  if (cell) {
    formulaText.value = cell.f ? `=${cell.f}` : (cell.v || '');
  } else {
    formulaText.value = '';
  }
};

const updateFormattingState = (cellRef) => {
  if (!workbook.value) return;

  const ws = workbook.value.Sheets[workbook.value.SheetNames[activeSheetIndex.value]];
  const cell = ws[cellRef];

  if (cell && cell.s) {
    isBold.value = !!cell.s.bold;
    isItalic.value = !!cell.s.italic;
    isUnderline.value = !!cell.s.underline;
    alignment.value = cell.s.hAlign || 'left';
    textColor.value = cell.s.fontColor ? `#${cell.s.fontColor}` : '#000000';
    backgroundColor.value = cell.s.bgColor ? `#${cell.s.bgColor}` : '#ffffff';
  } else {
    // Reset to defaults
    isBold.value = false;
    isItalic.value = false;
    isUnderline.value = false;
    alignment.value = 'left';
    textColor.value = '#000000';
    backgroundColor.value = '#ffffff';
  }
};

// Sheet management methods
const addNewSheet = () => {
  if (!workbook.value) return;

  saveState();
  const newSheetName = `Sheet${workbook.value.SheetNames.length + 1}`;
  workbook.value.SheetNames.push(newSheetName);
  workbook.value.Sheets[newSheetName] = XLSX.utils.aoa_to_sheet([[]]);

  markAsModified();
  activeSheetIndex.value = workbook.value.SheetNames.length - 1;
};

const showSheetContextMenu = (event, index) => {
  contextMenuSheetIndex.value = index;
  contextMenuStyle.value = {
    left: `${event.clientX}px`,
    top: `${event.clientY}px`
  };
  showContextMenu.value = true;

  // Close context menu when clicking elsewhere
  const closeMenu = () => {
    showContextMenu.value = false;
    document.removeEventListener('click', closeMenu);
  };
  setTimeout(() => document.addEventListener('click', closeMenu), 0);
};

const renameSheet = () => {
  const newName = prompt('Enter new sheet name:', workbook.value.SheetNames[contextMenuSheetIndex.value]);
  if (newName && newName !== workbook.value.SheetNames[contextMenuSheetIndex.value]) {
    saveState();
    const oldName = workbook.value.SheetNames[contextMenuSheetIndex.value];
    workbook.value.SheetNames[contextMenuSheetIndex.value] = newName;
    workbook.value.Sheets[newName] = workbook.value.Sheets[oldName];
    delete workbook.value.Sheets[oldName];
    markAsModified();
  }
  showContextMenu.value = false;
};

const duplicateSheet = () => {
  if (!workbook.value) return;

  saveState();
  const sourceSheet = workbook.value.Sheets[workbook.value.SheetNames[contextMenuSheetIndex.value]];
  const newSheetName = `${workbook.value.SheetNames[contextMenuSheetIndex.value]} Copy`;

  workbook.value.SheetNames.push(newSheetName);
  workbook.value.Sheets[newSheetName] = JSON.parse(JSON.stringify(sourceSheet));

  markAsModified();
  showContextMenu.value = false;
};

const deleteSheet = () => {
  if (!workbook.value || workbook.value.SheetNames.length <= 1) return;

  if (confirm('Are you sure you want to delete this sheet?')) {
    saveState();
    const sheetName = workbook.value.SheetNames[contextMenuSheetIndex.value];
    workbook.value.SheetNames.splice(contextMenuSheetIndex.value, 1);
    delete workbook.value.Sheets[sheetName];

    if (activeSheetIndex.value >= contextMenuSheetIndex.value && activeSheetIndex.value > 0) {
      activeSheetIndex.value--;
    }

    markAsModified();
  }
  showContextMenu.value = false;
};

// File operations
const downloadFile = () => {
  if (!workbook.value) return;

  const wb = XLSX.utils.book_new();
  workbook.value.SheetNames.forEach(sheetName => {
    wb.SheetNames.push(sheetName);
    wb.Sheets[sheetName] = workbook.value.Sheets[sheetName];
  });

  XLSX.writeFile(wb, fileName.value);
};

const printSheet = () => {
  if (!workbook.value) return;

  const table = grid.value?.querySelector('#spreadsheet-grid');
  if (!table) return;

  // Create a temporary print container
  const printContainer = document.createElement('div');
  printContainer.innerHTML = `
    <style>
      @media print {
        body * { visibility: hidden; }
        .print-container, .print-container * { visibility: visible; }
        .print-container { position: absolute; left: 0; top: 0; width: 100%; }
        table { border-collapse: collapse; width: 100%; }
        td, th { border: 1px solid #000; padding: 4px; font-size: 12px; }
      }
    </style>
    <div class="print-container">
      <h2>${fileName.value} - ${workbook.value.SheetNames[activeSheetIndex.value]}</h2>
      ${table.outerHTML}
    </div>
  `;

  document.body.appendChild(printContainer);
  window.print();
  document.body.removeChild(printContainer);
};

const renderSpreadsheet = () => {
  if (!grid.value || !workbook.value) return;

  grid.value.innerHTML = '';

  const ws = workbook.value.Sheets[workbook.value.SheetNames[activeSheetIndex.value]];
  if (!ws || !ws['!ref']) {
    // Create empty sheet if no data
    const table = document.createElement('table');
    table.id = 'spreadsheet-grid';

    // Create header row with column letters
    const headerRow = table.insertRow(-1);
    headerRow.insertCell(-1); // Empty corner cell
    for (let c = 0; c < 26; c++) {
      const th = document.createElement('th');
      th.textContent = String.fromCharCode(65 + c);
      th.className = 'column-header';
      headerRow.appendChild(th);
    }

    // Create rows with row numbers
    for (let r = 1; r <= 20; r++) {
      const tr = table.insertRow(-1);
      const rowHeader = document.createElement('th');
      rowHeader.textContent = r.toString();
      rowHeader.className = 'row-header';
      tr.appendChild(rowHeader);

      for (let c = 0; c < 26; c++) {
        const td = tr.insertCell(-1);
        td.setAttribute('contenteditable', 'true');
        td.setAttribute('data-cell', XLSX.utils.encode_cell({ r: r - 1, c }));
        td.addEventListener('input', handleCellInput);
        td.addEventListener('focus', () => {
          selectedCellRef.value = td.getAttribute('data-cell');
          updateFormulaBar(selectedCellRef.value);
          updateFormattingState(selectedCellRef.value);
        });
      }
    }

    grid.value.appendChild(table);
    return;
  }

  const table = document.createElement('table');
  table.id = 'spreadsheet-grid';

  const range = XLSX.utils.decode_range(ws['!ref']);
  const merges = ws['!merges'] || [];

  // Extend range to show more cells
  const extendedRange = {
    s: { r: 0, c: 0 },
    e: { r: Math.max(range.e.r, 19), c: Math.max(range.e.c, 25) }
  };

  // Create header row with column letters
  const headerRow = table.insertRow(-1);
  headerRow.insertCell(-1); // Empty corner cell
  for (let c = extendedRange.s.c; c <= extendedRange.e.c; c++) {
    const th = document.createElement('th');
    th.textContent = XLSX.utils.encode_col(c);
    th.className = 'column-header';
    headerRow.appendChild(th);
  }

  // Create a map for merged cells to handle them efficiently
  const mergedCells = {};
  merges.forEach(merge => {
    for (let r = merge.s.r; r <= merge.e.r; r++) {
      for (let c = merge.s.c; c <= merge.e.c; c++) {
        if (r === merge.s.r && c === merge.s.c) {
          mergedCells[`${r},${c}`] = {
            rowspan: merge.e.r - merge.s.r + 1,
            colspan: merge.e.c - merge.s.c + 1
          };
        } else {
          mergedCells[`${r},${c}`] = { hidden: true };
        }
      }
    }
  });

  for (let R = extendedRange.s.r; R <= extendedRange.e.r; ++R) {
    const tr = table.insertRow(-1);

    // Add row header
    const rowHeader = document.createElement('th');
    rowHeader.textContent = (R + 1).toString();
    rowHeader.className = 'row-header';
    tr.appendChild(rowHeader);

    for (let C = extendedRange.s.c; C <= extendedRange.e.c; ++C) {
      const cellInfo = mergedCells[`${R},${C}`];
      if (cellInfo && cellInfo.hidden) continue;

      const td = tr.insertCell(-1);
      td.setAttribute('contenteditable', 'true');

      const cell_ref = XLSX.utils.encode_cell({ c: C, r: R });
      td.setAttribute('data-cell', cell_ref);

      if (cellInfo) {
        if (cellInfo.rowspan > 1) td.rowSpan = cellInfo.rowspan;
        if (cellInfo.colspan > 1) td.colSpan = cellInfo.colspan;
      }

      const cell = ws[cell_ref];

      if (cell) {
        td.innerHTML = cell.w || cell.v || '';
        if (cell.s) {
          let style = '';
          if (cell.s.bgColor) {
            style += `background-color: #${cell.s.bgColor};`;
          }
          if (cell.s.fontColor) {
            style += `color: #${cell.s.fontColor};`;
          }
          if (cell.s.bold) {
            style += 'font-weight: bold;';
          }
          if (cell.s.italic) {
            style += 'font-style: italic;';
          }
          if (cell.s.underline) {
            style += 'text-decoration: underline;';
          }
          if (cell.s.hAlign) {
            style += `text-align: ${cell.s.hAlign};`;
          }
          if (cell.s.vAlign) {
            style += `vertical-align: ${cell.s.vAlign};`;
          }
          if (style) {
            td.setAttribute('style', style);
          }
        }
      }

      // Add event listeners
      td.addEventListener('input', handleCellInput);
      td.addEventListener('focus', () => {
        selectedCellRef.value = cell_ref;
        updateFormulaBar(cell_ref);
        updateFormattingState(cell_ref);
      });
    }
  }
  grid.value.appendChild(table);
};

const handleCellInput = (event) => {
  const cellRef = event.target.getAttribute('data-cell');
  const value = event.target.textContent;

  if (!workbook.value || !cellRef) return;

  const ws = workbook.value.Sheets[workbook.value.SheetNames[activeSheetIndex.value]];

  if (!ws[cellRef]) {
    ws[cellRef] = {};
  }

  const cell = ws[cellRef];
  cell.v = value;
  cell.w = value;

  // Determine cell type
  if (!isNaN(value) && value.trim() !== '') {
    cell.t = 'n';
    cell.v = Number(value);
  } else {
    cell.t = 's';
  }

  markAsModified();
  formulaText.value = value;
};

const saveChanges = async () => {
  if (!workbook.value || !hasUnsavedChanges.value) return;

  loading.value = true;
  error.value = null;

  try {
    // Send the updated workbook to the server
    const response = await fetch(`/api/save-file/${props.accountId}/${props.filePath}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ workbook: workbook.value })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to save file: ${errorData.error || response.statusText}`);
    }

    hasUnsavedChanges.value = false;
    emit('saved');

    // Show success message
    const successMsg = document.createElement('div');
    successMsg.className = 'save-success';
    successMsg.textContent = '✓ File saved successfully';
    document.body.appendChild(successMsg);
    setTimeout(() => {
      document.body.removeChild(successMsg);
    }, 3000);

  } catch (e) {
    error.value = e.message;
    console.error("Error saving XLSX data:", e);
  } finally {
    loading.value = false;
  }
};

// Watchers
watch(() => props.show, (newVal) => {
  if (newVal) {
    fetchXlsxData();
  } else {
    // Reset state when modal closes
    selectedCellRef.value = 'A1';
    formulaText.value = '';
    showContextMenu.value = false;
  }
});

watch(workbook, () => {
  if (workbook.value) {
    nextTick(renderSpreadsheet);
  }
});

watch(activeSheetIndex, () => {
  renderSpreadsheet();
  selectedCellRef.value = 'A1';
  formulaText.value = '';
});

// Close context menu when clicking elsewhere
watch(showContextMenu, (newVal) => {
  if (!newVal) {
    contextMenuSheetIndex.value = 0;
  }
});
</script>

<style scoped>
/* Modal and overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.modal-content {
  background: #ffffff;
  border-radius: 12px;
  max-width: 95vw;
  max-height: 95vh;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  border: 1px solid #e1e5e9;
}

/* Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom: 1px solid #e1e5e9;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-info {
  display: flex;
  gap: 12px;
  font-size: 12px;
  opacity: 0.9;
}

.sheet-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
}

.modified-indicator {
  color: #ffd700;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.header-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.header-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.save-btn {
  background: #28a745;
  border-color: #28a745;
}

.save-btn:hover:not(:disabled) {
  background: #218838;
}

.modal-close {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  font-size: 18px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.icon {
  font-size: 14px;
}

/* Loading and error states */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px 20px;
  color: #dc3545;
  font-size: 16px;
  text-align: center;
}

.error-icon {
  font-size: 32px;
}

.retry-btn {
  padding: 8px 16px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s ease;
}

.retry-btn:hover {
  background: #c82333;
}

/* Spreadsheet container */
.spreadsheet-container {
  display: flex;
  flex-direction: column;
  height: 85vh;
  width: 95vw;
  background: #f8f9fa;
}

/* Toolbar */
.toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e1e5e9;
  flex-wrap: wrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0 8px;
  border-right: 1px solid #e1e5e9;
}

.toolbar-section:last-child {
  border-right: none;
}

.toolbar-label {
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  margin-right: 4px;
}

.format-select {
  padding: 4px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
  background: white;
  cursor: pointer;
}

.format-btn, .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.format-btn:hover, .action-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.format-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.action-btn {
  width: auto;
  padding: 6px 10px;
  font-size: 11px;
  gap: 4px;
}

.action-btn.danger {
  color: #dc3545;
  border-color: #dc3545;
}

.action-btn.danger:hover {
  background: #dc3545;
  color: white;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.color-picker {
  width: 32px;
  height: 32px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  cursor: pointer;
  padding: 0;
}

/* Sheet tabs */
.sheet-tabs {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 8px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
  overflow-x: auto;
}

.sheet-tab {
  padding: 8px 16px;
  background: #e9ecef;
  border: 1px solid #ced4da;
  border-bottom: none;
  border-radius: 6px 6px 0 0;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: #495057;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 80px;
  text-align: center;
}

.sheet-tab:hover {
  background: #dee2e6;
}

.sheet-tab.active {
  background: white;
  color: #007bff;
  border-color: #007bff;
  border-bottom: 1px solid white;
  margin-bottom: -1px;
  z-index: 1;
  position: relative;
}

.add-sheet-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #ced4da;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #007bff;
  transition: all 0.2s ease;
  margin-left: 8px;
}

.add-sheet-btn:hover {
  background: #007bff;
  color: white;
}

/* Formula bar */
.formula-bar {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e1e5e9;
  gap: 12px;
}

.cell-reference {
  min-width: 80px;
}

.cell-ref-input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 13px;
  font-family: 'Courier New', monospace;
  text-align: center;
  font-weight: 600;
}

.formula-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
}

.formula-prefix {
  padding: 6px 8px;
  background: #f8f9fa;
  border-right: 1px solid #ced4da;
  font-size: 13px;
  font-weight: 600;
  color: #495057;
}

.formula-input {
  flex: 1;
  padding: 6px 8px;
  border: none;
  outline: none;
  font-size: 13px;
  font-family: 'Courier New', monospace;
}

/* Spreadsheet wrapper and grid */
.spreadsheet-wrapper {
  flex: 1;
  overflow: auto;
  background: white;
  border: 1px solid #e1e5e9;
  border-top: none;
}

.spreadsheet-grid {
  width: 100%;
  height: 100%;
  overflow: auto;
  position: relative;
}

/* Deep styles for the spreadsheet table */
:deep(#spreadsheet-grid) {
  border-collapse: collapse;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 13px;
}

:deep(#spreadsheet-grid th) {
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
  border: 1px solid #dee2e6;
  padding: 8px;
  font-weight: 600;
  color: #495057;
  text-align: center;
  position: sticky;
  top: 0;
  z-index: 10;
  user-select: none;
  font-size: 11px;
}

:deep(#spreadsheet-grid th.column-header) {
  min-width: 80px;
  width: 80px;
}

:deep(#spreadsheet-grid th.row-header) {
  min-width: 40px;
  width: 40px;
  left: 0;
  z-index: 11;
  background: linear-gradient(to right, #f8f9fa, #e9ecef);
}

:deep(#spreadsheet-grid td) {
  border: 1px solid #dee2e6;
  padding: 4px 8px;
  min-width: 80px;
  width: 80px;
  height: 24px;
  vertical-align: middle;
  background: white;
  transition: all 0.1s ease;
  cursor: cell;
}

:deep(#spreadsheet-grid td:hover) {
  background: #f8f9fa;
}

:deep(#spreadsheet-grid td:focus) {
  outline: 2px solid #007bff;
  outline-offset: -2px;
  background: #e3f2fd;
  z-index: 5;
  position: relative;
}

:deep(#spreadsheet-grid td[contenteditable="true"]:empty::before) {
  content: "";
  color: #adb5bd;
}

/* Context menu */
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  min-width: 160px;
  padding: 4px 0;
}

.context-item {
  display: block;
  width: 100%;
  padding: 8px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 13px;
  color: #495057;
  transition: background 0.2s ease;
}

.context-item:hover:not(:disabled) {
  background: #f8f9fa;
}

.context-item:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.context-item.danger {
  color: #dc3545;
}

.context-item.danger:hover:not(:disabled) {
  background: #f8d7da;
}

/* Success message */
:deep(.save-success) {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #28a745;
  color: white;
  padding: 12px 20px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1002;
  font-size: 14px;
  font-weight: 500;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .modal-content {
    max-width: 98vw;
    max-height: 98vh;
  }

  .spreadsheet-container {
    width: 98vw;
    height: 80vh;
  }

  .toolbar {
    flex-wrap: wrap;
    gap: 8px;
  }

  .toolbar-section {
    padding: 0 4px;
  }

  .header-btn {
    padding: 6px 8px;
    font-size: 12px;
  }

  .file-title {
    font-size: 16px;
  }

  :deep(#spreadsheet-grid th),
  :deep(#spreadsheet-grid td) {
    min-width: 60px;
    width: 60px;
    padding: 4px;
    font-size: 12px;
  }
}

/* Print styles */
@media print {
  .modal-overlay {
    position: static;
    background: none;
  }

  .modal-content {
    box-shadow: none;
    border: none;
    max-width: none;
    max-height: none;
  }

  .modal-header,
  .toolbar,
  .sheet-tabs,
  .formula-bar {
    display: none;
  }

  .spreadsheet-container {
    height: auto;
    width: 100%;
  }

  :deep(#spreadsheet-grid th),
  :deep(#spreadsheet-grid td) {
    border: 1px solid #000;
    padding: 4px;
  }
}
</style>