<template>
  <div class="file-uploader"
       @dragover.prevent="handleDragOver"
       @dragleave.prevent="handleDragLeave"
       @drop.prevent="handleDrop"
       :class="{ 'drag-over': isDraggingOver }">
    
    <input type="file" ref="fileInput" @change="handleFileChange" style="display: none;" />
    
    <div class="upload-area">
      <p>Drag and drop your file here, or</p>
      <button @click="triggerFileInput" :disabled="uploading" class="select-button">
        {{ uploading ? 'Uploading...' : 'Select File' }}
      </button>
      <div v-if="selectedFileName" class="selected-file">
        Selected: {{ selectedFileName }}
      </div>
    </div>

    <div v-if="message" :class="['message', messageType]">{{ message }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps({
  accountId: {
    type: String,
    required: true
  },
  currentPath: {
    type: String,
    default: '/'
  }
});

const fileInput = ref<HTMLInputElement | null>(null);
const file = ref<File | null>(null);
const uploading = ref(false);
const message = ref('');
const messageType = ref('');
const isDraggingOver = ref(false);
const selectedFileName = ref('');

const triggerFileInput = () => {
  fileInput.value?.click();
};

const handleFileChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    const selectedFile = target.files[0];
    file.value = selectedFile; // Keep track of the selected file
    selectedFileName.value = selectedFile.name;
    uploadFile(selectedFile); // Automatically upload the selected file
  }
};

const handleDragOver = (e: DragEvent) => {
  e.preventDefault();
  isDraggingOver.value = true;
};

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault();
  isDraggingOver.value = false;
};

const handleDrop = (e: DragEvent) => {
  e.preventDefault();
  isDraggingOver.value = false;

  if (e.dataTransfer?.files && e.dataTransfer.files.length > 0) {
    const droppedFile = e.dataTransfer.files[0];
    file.value = droppedFile;
    selectedFileName.value = droppedFile.name;
    uploadFile(droppedFile);
  }
};

const uploadFile = async (selectedFile: File) => {
  uploading.value = true;
  message.value = '';
  messageType.value = '';
  
  try {
    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('path', props.currentPath);
    
    const response = await fetch(`/api/upload-file/${props.accountId}`, {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    
    if (response.ok) {
      message.value = 'File uploaded successfully!';
      messageType.value = 'success';
      if (fileInput.value) fileInput.value.value = '';
      file.value = null;
      selectedFileName.value = '';
      emit('uploaded');
    } else {
      throw new Error(result.error || 'Failed to upload file');
    }
  } catch (error) {
    message.value = (error as Error).message || 'An error occurred during upload';
    messageType.value = 'error';
  } finally {
    uploading.value = false;
  }
};

  

const emit = defineEmits(['uploaded']);
</script>

<style scoped>
<style scoped>
.file-uploader {
  margin: 20px 0;
  padding: 20px; /* Increased padding */
  border: 2px dashed #ccc; /* Dashed border for drag area */
  border-radius: 8px;
  background-color: #f9f9f9;
  text-align: center; /* Center content */
  transition: border-color 0.3s ease; /* Smooth transition for drag effect */
  position: relative; /* Needed for absolute positioning of progress bar */
  overflow: hidden; /* Hide overflow of progress bar */
}

.file-uploader.drag-over {
  border-color: #007bff; /* Highlight on drag over */
  background-color: #e9f5ff; /* Light blue background on drag over */
}

.upload-area {
  display: flex;
  flex-direction: column; /* Stack elements vertically */
  align-items: center;
  gap: 15px; /* Increased gap */
  position: relative; /* Ensure upload area is above progress bar */
  z-index: 1; /* Ensure upload area is above progress bar */
}

.upload-area p {
  margin: 0;
  font-size: 1.1em;
  color: #555;
}

.select-button {
  padding: 10px 20px; /* Increased padding */
  background-color: #007bff; /* Primary button color */
  color: white;
  border: none;
  border-radius: 5px; /* Slightly larger border-radius */
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s ease; /* Smooth transition */
}

.select-button:hover {
  background-color: #0056b3; /* Darker shade on hover */
}

.select-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.selected-file {
  margin-top: 10px;
  font-style: italic;
  color: #333;
}

.progress-bar-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 10px; /* Height of the progress bar */
  background-color: #e0e0e0;
  z-index: 0; /* Ensure progress bar is below upload area */
}

.progress-bar {
  height: 100%;
  background-color: #4CAF50; /* Green color for progress */
  transition: width 0.3s ease; /* Smooth transition for progress */
}

.message {
  margin-top: 15px; /* Increased margin */
  padding: 12px; /* Increased padding */
  border-radius: 4px;
  font-weight: bold;
  position: relative; /* Ensure message is above progress bar */
  z-index: 1; /* Ensure message is above progress bar */
}

.message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
</style>