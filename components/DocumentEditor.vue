<template>
  <div v-if="show" class="modal-overlay" @click.self="$emit('close')">
    <div class="modal-content document-editor">
      <!-- Header -->
      <div class="editor-header">
        <div class="header-left">
          <button class="modal-close" @click="handleClose">&times;</button>
          <div class="document-info">
            <h2 class="document-title">{{ documentTitle }}</h2>
            <div class="document-meta">
              <span class="file-type-badge">{{ fileExtension.toUpperCase() }}</span>
              <span v-if="lastSaved" class="last-saved">Last saved: {{ formatTime(lastSaved) }}</span>
              <span v-if="hasUnsavedChanges" class="unsaved-indicator">Unsaved changes</span>
            </div>
          </div>
        </div>

        <div class="header-actions">
          <button @click="showSaveLocationModal = true" class="btn btn-primary">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
              <path d="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z" />
            </svg>
            Save As
          </button>
          <button v-if="canSave" @click="quickSave" class="btn btn-secondary" :disabled="saving">
            <svg v-if="saving" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="spinner">
              <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z" />
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
              <path d="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z" />
            </svg>
            Save
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="spinner">
            <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z" />
          </svg>
        </div>
        <p>Loading document...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="error-container">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="error-icon">
          <path d="M12,2L13.09,8.26L22,9L13.09,9.74L12,16L10.91,9.74L2,9L10.91,8.26L12,2Z" />
        </svg>
        <p class="error-message">{{ error }}</p>
        <button @click="retryLoad" class="btn btn-primary">Retry</button>
      </div>

      <!-- Editor Content -->
      <div v-else class="editor-container">
        <!-- Toolbar -->
        <div v-if="editorType === 'rich'" class="editor-toolbar">
          <div class="toolbar-group">
            <button @click="formatText('bold')" :class="{ active: isFormatActive('bold') }" class="toolbar-btn" title="Bold">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M13.5,15.5H10V12.5H13.5A1.5,1.5 0 0,1 15,14A1.5,1.5 0 0,1 13.5,15.5M10,6.5H13A1.5,1.5 0 0,1 14.5,8A1.5,1.5 0 0,1 13,9.5H10M15.6,10.79C16.57,10.11 17.25,9.02 17.25,8C17.25,5.74 15.5,4 13.25,4H7V18H14.04C16.14,18 17.75,16.3 17.75,14.21C17.75,12.69 16.89,11.39 15.6,10.79Z" />
              </svg>
            </button>
            <button @click="formatText('italic')" :class="{ active: isFormatActive('italic') }" class="toolbar-btn" title="Italic">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M10,4V7H12.21L8.79,15H6V18H14V15H11.79L15.21,7H18V4H10Z" />
              </svg>
            </button>
            <button @click="formatText('underline')" :class="{ active: isFormatActive('underline') }" class="toolbar-btn" title="Underline">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M5,21H19V19H5V21M12,17A6,6 0 0,0 18,11V3H15.5V11A3.5,3.5 0 0,1 12,14.5A3.5,3.5 0 0,1 8.5,11V3H6V11A6,6 0 0,0 12,17Z" />
              </svg>
            </button>
          </div>

          <div class="toolbar-separator"></div>

          <div class="toolbar-group">
            <button @click="formatText('header', '1')" class="toolbar-btn" title="Heading 1">H1</button>
            <button @click="formatText('header', '2')" class="toolbar-btn" title="Heading 2">H2</button>
            <button @click="formatText('header', '3')" class="toolbar-btn" title="Heading 3">H3</button>
          </div>

          <div class="toolbar-separator"></div>

          <div class="toolbar-group">
            <button @click="formatText('list', 'ordered')" class="toolbar-btn" title="Numbered List">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M7,13V11H21V13H7M7,19V17H21V19H7M7,7V5H21V7H7M3,8V5H2V4H4V8H3M2,17V16H5V20H2V19H4V18.5H3V17.5H4V17H2M4.25,10A0.75,0.75 0 0,1 5,10.75C5,10.95 4.92,11.14 4.79,11.27L3.12,13H5V14H2V13.08L4,11H2V10H4.25Z" />
              </svg>
            </button>
            <button @click="formatText('list', 'bullet')" class="toolbar-btn" title="Bullet List">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M7,5H21V7H7V5M7,13V11H21V13H7M4,4.5A1.5,1.5 0 0,1 5.5,6A1.5,1.5 0 0,1 4,7.5A1.5,1.5 0 0,1 2.5,6A1.5,1.5 0 0,1 4,4.5M4,10.5A1.5,1.5 0 0,1 5.5,12A1.5,1.5 0 0,1 4,13.5A1.5,1.5 0 0,1 2.5,12A1.5,1.5 0 0,1 4,10.5M7,19V17H21V19H7M4,16.5A1.5,1.5 0 0,1 5.5,18A1.5,1.5 0 0,1 4,19.5A1.5,1.5 0 0,1 2.5,18A1.5,1.5 0 0,1 4,16.5Z" />
              </svg>
            </button>
          </div>

          <div class="toolbar-separator"></div>

          <div class="toolbar-group">
            <button @click="insertLink" class="toolbar-btn" title="Insert Link">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3.9,12C3.9,10.29 5.29,8.9 7,8.9H11V7H7A5,5 0 0,0 2,12A5,5 0 0,0 7,17H11V15.1H7C5.29,15.1 3.9,13.71 3.9,12M8,13H16V11H8V13M17,7H13V8.9H17C18.71,8.9 20.1,10.29 20.1,12C20.1,13.71 18.71,15.1 17,15.1H13V17H17A5,5 0 0,0 22,12A5,5 0 0,0 17,7Z" />
              </svg>
            </button>
            <button @click="insertImage" class="toolbar-btn" title="Insert Image">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Editor Area -->
        <div class="editor-content">
          <!-- Rich Text Editor (Quill) -->
          <div v-if="editorType === 'rich'" ref="quillEditor" class="quill-editor"></div>

          <!-- Plain Text Editor -->
          <textarea
            v-else-if="editorType === 'plain'"
            v-model="plainTextContent"
            @input="handleContentChange"
            class="plain-text-editor"
            placeholder="Start typing your document..."
          ></textarea>

          <!-- Spreadsheet Editor -->
          <div v-else-if="editorType === 'spreadsheet'" class="spreadsheet-editor">
            <div class="sheet-tabs" v-if="workbook && workbook.SheetNames">
              <button
                v-for="(sheet, index) in workbook.SheetNames"
                :key="index"
                :class="{ active: activeSheetIndex === index }"
                @click="activeSheetIndex = index"
                class="sheet-tab"
              >
                {{ sheet }}
              </button>
            </div>
            <div class="spreadsheet-grid" ref="spreadsheetGrid"></div>
          </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
          <div class="status-left">
            <span class="word-count">{{ wordCount }} words</span>
            <span class="character-count">{{ characterCount }} characters</span>
          </div>
          <div class="status-right">
            <span v-if="autoSaveEnabled" class="auto-save-status">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M15,9H5V5H15V9Z" />
              </svg>
              Auto-save enabled
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Save Location Modal -->
    <SaveLocationModal
      :show="showSaveLocationModal"
      :fileName="documentTitle"
      :fileType="getFileType()"
      :accounts="accounts"
      :defaultAccount="defaultAccount"
      @close="showSaveLocationModal = false"
      @save="handleSaveAs"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import SaveLocationModal from './SaveLocationModal.vue';

// Import Quill dynamically to avoid SSR issues
let Quill = null;
if (process.client) {
  import('quill').then(module => {
    Quill = module.default;
  });
}

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  accountId: {
    type: String,
    default: ''
  },
  filePath: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: 'Untitled Document'
  },
  fileType: {
    type: String,
    default: 'docx' // docx, txt, md, html, xlsx, csv
  },
  accounts: {
    type: Array,
    default: () => []
  },
  defaultAccount: {
    type: Object,
    default: null
  },
  isNewDocument: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'saved']);

// State
const loading = ref(false);
const error = ref(null);
const saving = ref(false);
const hasUnsavedChanges = ref(false);
const lastSaved = ref(null);
const showSaveLocationModal = ref(false);

// Editor state
const editorType = ref('rich'); // 'rich', 'plain', 'spreadsheet'
const quillEditor = ref(null);
const quillInstance = ref(null);
const plainTextContent = ref('');
const workbook = ref(null);
const activeSheetIndex = ref(0);
const spreadsheetGrid = ref(null);

// Document state
const documentTitle = ref('');
const originalContent = ref('');
const currentSavePath = ref('');
const currentSaveAccount = ref(null);

// Settings
const autoSaveEnabled = ref(true);
const autoSaveInterval = ref(null);

// Computed properties
const fileExtension = computed(() => {
  if (props.filePath) {
    return props.filePath.split('.').pop() || props.fileType;
  }
  return props.fileType;
});

const canSave = computed(() => {
  return currentSavePath.value && currentSaveAccount.value && hasUnsavedChanges.value;
});

const wordCount = computed(() => {
  const content = getPlainTextContent();
  return content.trim() ? content.trim().split(/\s+/).length : 0;
});

const characterCount = computed(() => {
  return getPlainTextContent().length;
});

// Methods
const initializeEditor = async () => {
  if (!props.show) return;

  loading.value = true;
  error.value = null;

  try {
    // Set document title
    documentTitle.value = props.fileName || 'Untitled Document';

    // Determine editor type based on file extension
    const ext = fileExtension.value.toLowerCase();
    if (['xlsx', 'xls', 'csv'].includes(ext)) {
      editorType.value = 'spreadsheet';
    } else if (['txt', 'md'].includes(ext)) {
      editorType.value = 'plain';
    } else {
      editorType.value = 'rich';
    }

    if (props.isNewDocument) {
      // Initialize empty document
      await initializeEmptyDocument();
    } else {
      // Load existing document
      await loadDocument();
    }

    // Initialize the appropriate editor
    await nextTick();
    if (editorType.value === 'rich') {
      await initializeQuillEditor();
    } else if (editorType.value === 'spreadsheet') {
      await initializeSpreadsheetEditor();
    }

    // Set up auto-save
    if (autoSaveEnabled.value && !props.isNewDocument) {
      setupAutoSave();
    }

  } catch (err) {
    error.value = err.message || 'Failed to initialize editor';
    console.error('Editor initialization error:', err);
  } finally {
    loading.value = false;
  }
};

const initializeEmptyDocument = async () => {
  if (editorType.value === 'spreadsheet') {
    // Initialize empty workbook
    const XLSX = await import('xlsx');
    workbook.value = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet([['']]);
    XLSX.utils.book_append_sheet(workbook.value, ws, 'Sheet1');
  } else {
    plainTextContent.value = '';
  }
  originalContent.value = '';
  hasUnsavedChanges.value = false;
};

const loadDocument = async () => {
  if (!props.accountId || !props.filePath) {
    throw new Error('Account ID and file path are required');
  }

  const response = await fetch(`/api/file-content/${props.accountId}/${encodeURIComponent(props.filePath)}`);

  if (!response.ok) {
    throw new Error(`Failed to load document: ${response.statusText}`);
  }

  const ext = fileExtension.value.toLowerCase();

  if (['xlsx', 'xls'].includes(ext)) {
    // Load as workbook
    const workbookResponse = await fetch(`/api/file-content/${props.accountId}/${encodeURIComponent(props.filePath)}?format=workbook`);
    workbook.value = await workbookResponse.json();
  } else {
    // Load as text
    const content = await response.text();

    if (ext === 'docx') {
      // Convert DOCX to HTML using mammoth
      const mammoth = await import('mammoth');
      const arrayBuffer = await response.arrayBuffer();
      const result = await mammoth.convertToHtml({ arrayBuffer });
      originalContent.value = result.value;
    } else {
      originalContent.value = content;
    }

    plainTextContent.value = originalContent.value;
  }

  // Set current save location
  currentSavePath.value = props.filePath;
  currentSaveAccount.value = props.accounts.find(acc => acc.id === props.accountId);
  hasUnsavedChanges.value = false;
};

const initializeQuillEditor = async () => {
  if (!Quill || !quillEditor.value) return;

  const toolbarOptions = [
    ['bold', 'italic', 'underline'],
    [{ 'header': [1, 2, 3, false] }],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    ['link', 'image'],
    ['clean']
  ];

  quillInstance.value = new Quill(quillEditor.value, {
    theme: 'snow',
    modules: {
      toolbar: false // We'll use our custom toolbar
    }
  });

  // Set initial content
  if (originalContent.value) {
    if (fileExtension.value.toLowerCase() === 'html') {
      quillInstance.value.root.innerHTML = originalContent.value;
    } else {
      quillInstance.value.setText(originalContent.value);
    }
  }

  // Listen for content changes
  quillInstance.value.on('text-change', handleContentChange);
};

const initializeSpreadsheetEditor = async () => {
  if (!workbook.value || !spreadsheetGrid.value) return;

  const XLSX = await import('xlsx');
  renderSpreadsheet();
};

const renderSpreadsheet = async () => {
  if (!workbook.value || !spreadsheetGrid.value) return;

  const XLSX = await import('xlsx');
  spreadsheetGrid.value.innerHTML = '';

  const ws = workbook.value.Sheets[workbook.value.SheetNames[activeSheetIndex.value]];
  if (!ws || !ws['!ref']) return;

  const table = document.createElement('table');
  table.className = 'spreadsheet-table';

  const range = XLSX.utils.decode_range(ws['!ref']);

  for (let R = range.s.r; R <= range.e.r; ++R) {
    const tr = table.insertRow(-1);
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const td = tr.insertCell(-1);
      td.setAttribute('contenteditable', 'true');
      td.addEventListener('input', handleSpreadsheetCellChange);

      const cell_ref = XLSX.utils.encode_cell({ c: C, r: R });
      const cell = ws[cell_ref];

      if (cell) {
        td.textContent = cell.w || cell.v || '';
      }
    }
  }

  spreadsheetGrid.value.appendChild(table);
};

const handleContentChange = useDebounceFn(() => {
  hasUnsavedChanges.value = true;
}, 500);

const handleSpreadsheetCellChange = (event) => {
  hasUnsavedChanges.value = true;
  // Update workbook data
  // Implementation would update the workbook.value with new cell data
};

const getPlainTextContent = () => {
  if (editorType.value === 'rich' && quillInstance.value) {
    return quillInstance.value.getText();
  } else if (editorType.value === 'plain') {
    return plainTextContent.value;
  } else if (editorType.value === 'spreadsheet') {
    // Return a text representation of the spreadsheet
    return 'Spreadsheet content';
  }
  return '';
};

const formatText = (format, value = null) => {
  if (!quillInstance.value) return;

  const range = quillInstance.value.getSelection();
  if (!range) return;

  switch (format) {
    case 'bold':
    case 'italic':
    case 'underline':
      quillInstance.value.format(format, !isFormatActive(format));
      break;
    case 'header':
      quillInstance.value.format('header', value === '1' ? 1 : value === '2' ? 2 : value === '3' ? 3 : false);
      break;
    case 'list':
      quillInstance.value.format('list', value);
      break;
  }
};

const isFormatActive = (format) => {
  if (!quillInstance.value) return false;
  const range = quillInstance.value.getSelection();
  if (!range) return false;
  const formats = quillInstance.value.getFormat(range);
  return !!formats[format];
};

const insertLink = () => {
  const url = prompt('Enter URL:');
  if (url && quillInstance.value) {
    const range = quillInstance.value.getSelection();
    if (range) {
      quillInstance.value.insertText(range.index, url, 'link', url);
    }
  }
};

const insertImage = () => {
  const url = prompt('Enter image URL:');
  if (url && quillInstance.value) {
    const range = quillInstance.value.getSelection();
    if (range) {
      quillInstance.value.insertEmbed(range.index, 'image', url);
    }
  }
};

const quickSave = async () => {
  if (!canSave.value) return;

  saving.value = true;
  try {
    await saveDocument(currentSaveAccount.value, currentSavePath.value, fileExtension.value);
    hasUnsavedChanges.value = false;
    lastSaved.value = new Date();
  } catch (err) {
    error.value = err.message || 'Failed to save document';
  } finally {
    saving.value = false;
  }
};

const handleSaveAs = async (saveData) => {
  saving.value = true;
  try {
    await saveDocument(saveData.account, saveData.fullPath, saveData.format);

    // Update current save location
    currentSavePath.value = saveData.fullPath;
    currentSaveAccount.value = saveData.account;
    documentTitle.value = saveData.fileName;

    hasUnsavedChanges.value = false;
    lastSaved.value = new Date();
    showSaveLocationModal.value = false;

    emit('saved', saveData);
  } catch (err) {
    error.value = err.message || 'Failed to save document';
  } finally {
    saving.value = false;
  }
};

const saveDocument = async (account, filePath, format) => {
  const content = getCurrentContent();

  const response = await fetch(`/api/save-document/${account.id}/${encodeURIComponent(filePath)}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      content,
      format,
      type: editorType.value
    })
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || 'Failed to save document');
  }

  return response.json();
};

const getCurrentContent = () => {
  if (editorType.value === 'rich' && quillInstance.value) {
    return {
      html: quillInstance.value.root.innerHTML,
      text: quillInstance.value.getText()
    };
  } else if (editorType.value === 'plain') {
    return {
      text: plainTextContent.value
    };
  } else if (editorType.value === 'spreadsheet') {
    return {
      workbook: workbook.value
    };
  }
  return { text: '' };
};

const getFileType = () => {
  const ext = fileExtension.value.toLowerCase();
  if (['xlsx', 'xls', 'csv'].includes(ext)) {
    return 'spreadsheet';
  } else if (['txt', 'md'].includes(ext)) {
    return 'text';
  }
  return 'document';
};

const formatTime = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date);
};

const setupAutoSave = () => {
  if (autoSaveInterval.value) {
    clearInterval(autoSaveInterval.value);
  }

  autoSaveInterval.value = setInterval(() => {
    if (hasUnsavedChanges.value && canSave.value) {
      quickSave();
    }
  }, 30000); // Auto-save every 30 seconds
};

const retryLoad = () => {
  error.value = null;
  initializeEditor();
};

const handleClose = () => {
  if (hasUnsavedChanges.value) {
    const shouldClose = confirm('You have unsaved changes. Are you sure you want to close?');
    if (!shouldClose) return;
  }

  emit('close');
};

// Lifecycle hooks
watch(() => props.show, (newVal) => {
  if (newVal) {
    initializeEditor();
  } else {
    // Cleanup
    if (autoSaveInterval.value) {
      clearInterval(autoSaveInterval.value);
      autoSaveInterval.value = null;
    }
    if (quillInstance.value) {
      quillInstance.value = null;
    }
  }
});

watch(activeSheetIndex, () => {
  if (editorType.value === 'spreadsheet') {
    renderSpreadsheet();
  }
});

onUnmounted(() => {
  if (autoSaveInterval.value) {
    clearInterval(autoSaveInterval.value);
  }
});
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.document-editor {
  width: 95%;
  max-width: 1200px;
  height: 90vh;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  transition: all 0.2s;
}

.modal-close:hover {
  background: #e5e7eb;
  color: #374151;
}

.document-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.document-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.document-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.file-type-badge {
  background: #3b82f6;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
  text-transform: uppercase;
}

.last-saved {
  color: #059669;
}

.unsaved-indicator {
  color: #dc2626;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn svg {
  width: 1rem;
  height: 1rem;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #4b5563;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: 1rem;
  color: #6b7280;
}

.loading-spinner svg,
.error-icon {
  width: 3rem;
  height: 3rem;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-message {
  color: #dc2626;
  text-align: center;
  margin: 0;
}

.editor-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e5e7eb;
  background: white;
  flex-wrap: wrap;
}

.toolbar-group {
  display: flex;
  gap: 0.25rem;
}

.toolbar-separator {
  width: 1px;
  height: 1.5rem;
  background: #e5e7eb;
  margin: 0 0.5rem;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: none;
  border: 1px solid transparent;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s;
  color: #374151;
  font-size: 0.75rem;
  font-weight: 500;
}

.toolbar-btn:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.toolbar-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.toolbar-btn svg {
  width: 1rem;
  height: 1rem;
}

.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.quill-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.plain-text-editor {
  flex: 1;
  padding: 1.5rem;
  border: none;
  outline: none;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  resize: none;
}

.spreadsheet-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sheet-tabs {
  display: flex;
  gap: 0.25rem;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.sheet-tab {
  padding: 0.5rem 1rem;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem 0.375rem 0 0;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.sheet-tab:hover {
  background: #f3f4f6;
}

.sheet-tab.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.spreadsheet-grid {
  flex: 1;
  overflow: auto;
  padding: 1rem;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  font-size: 0.75rem;
  color: #6b7280;
}

.status-left {
  display: flex;
  gap: 1rem;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auto-save-status {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #059669;
}

.auto-save-status svg {
  width: 0.875rem;
  height: 0.875rem;
}
</style>

<style>
/* Global Quill styles */
.ql-editor {
  padding: 1.5rem !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;
  font-size: 1rem !important;
  line-height: 1.6 !important;
}

.ql-editor.ql-blank::before {
  color: #9ca3af !important;
  font-style: italic !important;
}

.spreadsheet-table {
  border-collapse: collapse;
  width: 100%;
  min-width: 600px;
}

.spreadsheet-table td {
  border: 1px solid #d1d5db;
  padding: 0.5rem;
  min-width: 100px;
  min-height: 2rem;
  vertical-align: top;
}

.spreadsheet-table td:focus {
  outline: 2px solid #3b82f6;
  background: #eff6ff;
}
</style>