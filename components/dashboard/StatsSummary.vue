<template>
  <div class="dashboard-card">
    <div class="card-header">
      <h3>Accounts Summary</h3>
      <span class="actions">...</span>
    </div>
    <div class="stats-row">
      <div class="stat-item">
        <div class="icon-container total">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>
        </div>
        <div class="text-content">
          <span class="stat-value">{{ Math.round(displayedTotal) }}</span>
          <span class="stat-label">Total Accounts</span>
        </div>
      </div>
      <div class="stat-item">
        <div class="icon-container connected">
           <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.72"/><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.72-1.72"/></svg>
        </div>
        <div class="text-content">
          <span class="stat-value text-green">{{ Math.round(displayedConnected) }}</span>
          <span class="stat-label">Connected</span>
        </div>
      </div>
      <div class="stat-item">
        <div class="icon-container disconnected">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m14.12 14.12 2.83 2.83M10.88 10.88 8.05 8.05"/><path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.72"/><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.72-1.72"/><line x1="2" x2="22" y1="2" y2="22"/></svg>
        </div>
        <div class="text-content">
          <span class="stat-value text-red">{{ Math.round(displayedDisconnected) }}</span>
          <span class="stat-label">Disconnected</span>
        </div>
      </div>
    </div>
    <div class="proportion-bar-container">
        <div class="proportion-bar-label">
            <span>Connection Health</span>
            <span>{{ connectedPercentage.toFixed(0) }}%</span>
        </div>
        <div class="bar-background">
            <div class="bar-foreground" :style="{ width: `${connectedPercentage}%` }"></div>
        </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, computed, type Ref } from 'vue';
const props = defineProps({
  totalAccounts: { type: Number, required: true, default: 0 },
  connectedAccounts: { type: Number, required: true, default: 0 },
  disconnectedAccounts: { type: Number, required: true, default: 0 }
});
const displayedTotal = ref(props.totalAccounts);
const displayedConnected = ref(props.connectedAccounts);
const displayedDisconnected = ref(props.disconnectedAccounts);
function animateValue(targetRef: Ref<number>, endValue: number, duration = 500) {
  const startValue = targetRef.value;
  const range = endValue - startValue;
  let startTime: number | null = null;
  function step(currentTime: number) {
    if (!startTime) startTime = currentTime;
    const progress = Math.min((currentTime - startTime) / duration, 1);
    targetRef.value = startValue + range * progress;
    if (progress < 1) { requestAnimationFrame(step); }
    else { targetRef.value = endValue; }
  }
  requestAnimationFrame(step);
}
watch(() => props.totalAccounts, (newValue, oldValue) => animateValue(displayedTotal, newValue));
watch(() => props.connectedAccounts, (newValue, oldValue) => animateValue(displayedConnected, newValue));
watch(() => props.disconnectedAccounts, (newValue, oldValue) => animateValue(displayedDisconnected, newValue));
const connectedPercentage = computed(() => {
    if (props.totalAccounts === 0) return 0;
    return (props.connectedAccounts / props.totalAccounts) * 100;
});
</script>
<style scoped>
.dashboard-card { background: white; border-radius: 20px; padding: 24px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05); border: 1px solid #e2e8f0; }
.card-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
.card-header h3 { color: #1a202c; margin: 0; font-size: 1.25em; font-weight: 600; }
.actions { color: #a0aec0; font-weight: bold; cursor: pointer; }
.stats-row { display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; }
.stat-item { display: flex; align-items: center; gap: 16px; background: #f7fafc; padding: 16px; border-radius: 12px; border: 1px solid #e2e8f0; transition: all 0.2s ease-in-out; }
.stat-item:hover { transform: translateY(-4px); box-shadow: 0 4px 15px rgba(0,0,0,0.05); }
.icon-container { flex-shrink: 0; display: flex; align-items: center; justify-content: center; width: 48px; height: 48px; border-radius: 50%; }
.icon-container.total { background-color: #e9efff; color: #4a6cf7; }
.icon-container.connected { background-color: #e6f7ee; color: #10b981; }
.icon-container.disconnected { background-color: #fef2f2; color: #ef4444; }
.text-content { display: flex; flex-direction: column; }
.stat-value { font-size: 2em; font-weight: 700; color: #1a202c; line-height: 1.1; }
.stat-value.text-green { color: #10b981; }
.stat-value.text-red { color: #ef4444; }
.stat-label { font-size: 0.875em; color: #718096; font-weight: 500; }
.proportion-bar-container { margin-top: 24px; }
.proportion-bar-label { display: flex; justify-content: space-between; font-size: 0.875rem; color: #4a5568; margin-bottom: 8px; font-weight: 500; }
.bar-background { width: 100%; height: 8px; background-color: #e2e8f0; border-radius: 4px; overflow: hidden; }
.bar-foreground { height: 100%; background-color: #10b981; border-radius: 4px; transition: width 0.5s ease-in-out; }
@media (max-width: 1200px) { .stats-row { grid-template-columns: 1fr; } }
</style>