<template>
  <div v-if="isOpen" class="modal-overlay" @click.self="closeModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Add Storage Account</h3>
        <button class="close-button" @click="closeModal">×</button>
      </div>
      <div class="modal-body">
        <form @submit.prevent="submitForm">
          <div class="form-group">
            <label for="providerType">Storage Type</label>
            <select v-model="formData.provider" id="providerType" class="form-select" required>
              <option value="">Select storage type</option>
              <option value="local">Local Drive</option>
              <option value="webdav">WebDAV Cloud Storage</option>
            </select>
          </div>

          <div class="form-group">
            <label for="accountName">Account Name</label>
            <input
              v-model="formData.name"
              type="text"
              id="accountName"
              :placeholder="formData.provider === 'local' ? 'e.g. My Local Drive' : 'e.g. Personal WebDAV'"
              required
            />
          </div>
          <!-- Local Drive Fields -->
          <div v-if="formData.provider === 'local'" class="form-group">
            <label for="localPath">Local Directory Path</label>
            <div class="path-input-group">
              <input
                v-model="formData.localPath"
                type="text"
                id="localPath"
                placeholder="e.g. /home/<USER>/Documents or C:\Users\<USER>\Documents"
                required
              />
              <button type="button" class="browse-button" @click="openDirectoryPicker">
                📁 Browse
              </button>
            </div>
            <small class="form-help">Enter the full path to the directory you want to use as storage</small>
          </div>

          <!-- WebDAV Fields -->
          <template v-if="formData.provider === 'webdav'">
            <div class="form-group">
              <label for="serverUrl">Server URL</label>
              <input
                v-model="formData.url"
                type="url"
                id="serverUrl"
                placeholder="https://example.com/webdav"
                required
              />
            </div>
            <div class="form-group">
              <label for="username">Username</label>
              <input
                v-model="formData.username"
                type="text"
                id="username"
                required
              />
            </div>
            <div class="form-group">
              <label for="password">Password</label>
              <input
                v-model="formData.password"
                type="password"
                id="password"
                required
              />
            </div>
          </template>
          <div class="form-actions">
            <button type="button" class="btn-cancel" @click="closeModal">
              Cancel
            </button>
            <button type="submit" class="btn-submit">Add Account</button>
          </div>
        </form>
      </div>
    </div>

    <!-- Directory Picker Modal -->
    <div v-if="showDirectoryPicker" class="modal-overlay" @click.self="closeDirectoryPicker">
      <div class="directory-picker-modal">
        <div class="modal-header">
          <h3>{{ isDriveView ? 'Select Drive/Volume' : 'Select Directory' }}</h3>
          <button class="close-button" @click="closeDirectoryPicker">×</button>
        </div>
        <div class="directory-picker-body">
          <!-- Current Path Display -->
          <div v-if="!isDriveView" class="current-path">
            <span class="path-label">Current Path:</span>
            <span class="path-value">{{ currentBrowsePath }}</span>
          </div>

          <!-- Navigation -->
          <div class="directory-navigation">
            <button
              v-if="!isDriveView"
              class="nav-button drives-button"
              @click="showDrives"
              :disabled="loadingDirectories"
            >
              💾 Show Drives
            </button>
            <button
              v-if="parentPath && !isDriveView"
              class="nav-button up-button"
              @click="navigateToParent"
              :disabled="loadingDirectories"
            >
              ⬆️ Up
            </button>
            <button
              class="nav-button refresh-button"
              @click="loadDirectories"
              :disabled="loadingDirectories"
            >
              🔄 Refresh
            </button>
          </div>

          <!-- Directory List -->
          <div class="directory-list">
            <div v-if="loadingDirectories" class="loading">
              Loading directories...
            </div>
            <div v-else-if="directoryError" class="error">
              {{ directoryError }}
            </div>
            <div v-else-if="directories.length === 0" class="empty">
              {{ isDriveView ? 'No drives/volumes found' : 'No accessible directories found' }}
            </div>
            <div v-else>
              <div
                v-for="dir in directories"
                :key="dir.path"
                class="directory-item"
                :class="{
                  'not-accessible': !dir.isAccessible,
                  'drive-item': dir.type === 'drive'
                }"
                @click="dir.isAccessible && navigateToDirectory(dir.path)"
              >
                <span class="directory-icon">
                  {{ dir.type === 'drive' ? '💾' : '📁' }}
                </span>
                <span class="directory-name">{{ dir.name }}</span>
                <span v-if="!dir.isAccessible" class="access-indicator">🔒</span>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="directory-picker-actions">
            <button type="button" class="btn-cancel" @click="closeDirectoryPicker">
              Cancel
            </button>
            <button
              type="button"
              class="btn-select"
              @click="selectCurrentDirectory"
              :disabled="!currentBrowsePath"
            >
              Select This Directory
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps({
  isOpen: Boolean,
});

const emit = defineEmits(['close', 'add-account']);

const formData = ref({
  provider: '',
  name: '',
  url: 'https://mori.teracloud.jp/dav/',
  username: '',
  password: '',
  localPath: '',
});

// Directory picker state
const showDirectoryPicker = ref(false);
const currentBrowsePath = ref('');
const parentPath = ref(null);
const directories = ref([]);
const loadingDirectories = ref(false);
const directoryError = ref('');
const isDriveView = ref(true);

// Directory picker state
const showDirectoryPicker = ref(false);
const currentBrowsePath = ref('');
const parentPath = ref(null);
const directories = ref([]);
const loadingDirectories = ref(false);
const directoryError = ref('');

const closeModal = () => {
  emit('close');
};

const submitForm = () => {
  emit('add-account', { ...formData.value });
  formData.value = {
    provider: '',
    name: '',
    url: 'https://mori.teracloud.jp/dav/',
    username: '',
    password: '',
    localPath: ''
  };
  closeModal();
};

// Directory picker functions
const loadDirectories = async (path = null) => {
  loadingDirectories.value = true;
  directoryError.value = '';

  try {
    const params = new URLSearchParams();
    if (path) {
      params.append('path', path);
      isDriveView.value = false;
    } else if (isDriveView.value) {
      params.append('drives', 'true');
    } else if (currentBrowsePath.value) {
      params.append('path', currentBrowsePath.value);
    } else {
      params.append('drives', 'true');
      isDriveView.value = true;
    }

    const response = await fetch(`/api/browse-directories?${params}`);
    const data = await response.json();

    if (data.error) {
      directoryError.value = data.error;
      directories.value = [];
    } else {
      directories.value = data.directories || [];
      currentBrowsePath.value = data.currentPath || '';
      parentPath.value = data.parentPath;
      isDriveView.value = data.isDriveView || false;
    }
  } catch (error) {
    directoryError.value = 'Failed to load directories';
    directories.value = [];
  } finally {
    loadingDirectories.value = false;
  }
};

const navigateToDirectory = (path) => {
  loadDirectories(path);
};

const navigateToParent = () => {
  if (parentPath.value) {
    loadDirectories(parentPath.value);
  }
};

const showDrives = () => {
  isDriveView.value = true;
  currentBrowsePath.value = '';
  parentPath.value = null;
  loadDirectories();
};

const selectCurrentDirectory = () => {
  if (currentBrowsePath.value) {
    formData.value.localPath = currentBrowsePath.value;
    closeDirectoryPicker();
  }
};

const closeDirectoryPicker = () => {
  showDirectoryPicker.value = false;
  isDriveView.value = true;
  currentBrowsePath.value = '';
  directories.value = [];
  directoryError.value = '';
};

// Open directory picker and load drives
const openDirectoryPicker = () => {
  showDirectoryPicker.value = true;
  isDriveView.value = true;
  loadDirectories();
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 500px;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.4rem;
  color: #2d3748;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.8rem;
  cursor: pointer;
  color: #718096;
  line-height: 1;
}

.modal-body {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #4a5568;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
  border-color: #4299e1;
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
}

.form-help {
  display: block;
  margin-top: 4px;
  font-size: 0.85rem;
  color: #718096;
}

.path-input-group {
  display: flex;
  gap: 8px;
}

.path-input-group input {
  flex: 1;
}

.browse-button {
  padding: 10px 16px;
  background: #e2e8f0;
  border: 1px solid #cbd5e0;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  white-space: nowrap;
  transition: background-color 0.2s;
}

.browse-button:hover {
  background: #cbd5e0;
}

/* Directory Picker Modal Styles */
.directory-picker-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.directory-picker-body {
  padding: 20px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.current-path {
  margin-bottom: 15px;
  padding: 10px;
  background: #f7fafc;
  border-radius: 6px;
  font-family: monospace;
  font-size: 0.9rem;
}

.path-label {
  font-weight: 600;
  color: #4a5568;
}

.path-value {
  color: #2d3748;
  word-break: break-all;
}

.directory-navigation {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
}

.nav-button {
  padding: 8px 12px;
  background: #e2e8f0;
  border: 1px solid #cbd5e0;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: background-color 0.2s;
}

.nav-button:hover:not(:disabled) {
  background: #cbd5e0;
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.directory-list {
  flex: 1;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  overflow-y: auto;
  max-height: 300px;
}

.loading, .error, .empty {
  padding: 20px;
  text-align: center;
  color: #718096;
}

.error {
  color: #e53e3e;
}

.directory-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  transition: background-color 0.2s;
}

.directory-item:hover:not(.not-accessible) {
  background: #f7fafc;
}

.directory-item:last-child {
  border-bottom: none;
}

.directory-item.not-accessible {
  opacity: 0.5;
  cursor: not-allowed;
}

.directory-item.drive-item {
  background: #f0f9ff;
  border-left: 3px solid #3b82f6;
}

.directory-icon {
  margin-right: 12px;
  font-size: 1.2rem;
}

.directory-name {
  flex: 1;
  font-weight: 500;
}

.access-indicator {
  margin-left: 8px;
  opacity: 0.7;
}

.directory-picker-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e2e8f0;
}

.btn-select {
  padding: 10px 20px;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
}

.btn-select:hover:not(:disabled) {
  background: #3182ce;
}

.btn-select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.btn-cancel {
  padding: 10px 20px;
  background: #e2e8f0;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  color: #4a5568;
}

.btn-submit {
  padding: 10px 20px;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
}

.btn-submit:hover {
  background: #3182ce;
}
</style>