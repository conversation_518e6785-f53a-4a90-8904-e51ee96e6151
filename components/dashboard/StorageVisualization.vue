<template>
  <div class="dashboard-card">
    <div class="card-header">
      <div class="header-left">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><ellipse cx="12" cy="5" rx="9" ry="3"/><path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"/><path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"/></svg>
        <h3>Storage Usage</h3>
      </div>
      <button @click="$emit('refresh')" class="refresh-btn" :disabled="isLoading" title="Refresh storage data">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" :class="{ 'spinning': isLoading }">
          <polyline points="23,4 23,10 17,10"></polyline>
          <polyline points="1,20 1,14 7,14"></polyline>
          <path d="M20.49,9A9,9,0,0,0,5.64,5.64L1,10m22,4L18.36,18.36A9,9,0,0,1,3.51,15"></path>
        </svg>
      </button>
    </div>

    <div class="chart-container">
      <svg class="donut-chart" viewBox="0 0 120 120">
        <circle class="donut-chart-bg" cx="60" cy="60" :r="radius" />

        <circle
          class="donut-chart-fg"
          :stroke="progressColor"
          :stroke-dasharray="circumference"
          :stroke-dashoffset="strokeOffset"
          cx="60"
          cy="60"
          :r="radius"
        />

        <text class="chart-text-percent" x="50%" y="50%" dy=".3em">
          {{ isLoading ? '...' : Math.round(animatedPercentage) + '%' }}
        </text>
      </svg>
    </div>

    <div class="storage-details">
      <div class="primary-stats">
        <div class="used-storage">
          <span class="value">{{ isLoading ? 'Loading...' : formatBytes(animatedUsedBytes) }}</span>
          <span class="label">Used</span>
        </div>
        <div class="total-storage">
          <span class="value">{{ totalQuotaLimitGB }} GB</span>
          <span class="label">Total Quota</span>
        </div>
      </div>

      <div class="storage-summary" v-if="!isLoading">
        <div class="summary-item">
          <span class="summary-label">Available:</span>
          <span class="summary-value">{{ formatBytes(availableBytes) }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Connected Accounts:</span>
          <span class="summary-value">{{ connectedAccountsCount }} / {{ storageAccounts.length }}</span>
        </div>
      </div>

      <div class="detailed-breakdown" v-if="showBreakdown">
        <div class="breakdown-header">
          <h4>Storage Breakdown</h4>
          <button @click="toggleBreakdown" class="toggle-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="18,15 12,9 6,15"></polyline>
            </svg>
          </button>
        </div>

        <div class="accounts-breakdown">
          <div v-for="account in storageAccounts" :key="account.accountId" class="account-item">
            <div class="account-info">
              <span class="account-name">{{ account.accountName }}</span>
              <div class="account-status">
                <span :class="['status-indicator', account.isConnected ? 'connected' : 'disconnected']"></span>
                <span class="status-text">{{ account.isConnected ? 'Connected' : 'Disconnected' }}</span>
              </div>
            </div>
            <div class="account-storage">
              <span class="storage-used">{{ formatBytes(account.usedBytes) }}</span>
              <span class="storage-quota">/ {{ account.quotaLimitGB }} GB</span>
            </div>
          </div>
        </div>
      </div>

      <div class="breakdown-toggle" v-else>
        <button @click="toggleBreakdown" class="toggle-btn">
          <span>View Breakdown</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="6,9 12,15 18,9"></polyline>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

interface StorageAccount {
  accountId: string;
  accountName: string;
  usedBytes: number;
  quotaLimitGB: number;
  isConnected: boolean;
  error?: string;
}

const props = defineProps({
  // The amount of storage used in bytes
  usedBytes: {
    type: Number,
    default: 0
  },
  // The total quota limit in GB
  totalQuotaLimitGB: {
    type: Number,
    default: 0
  },
  // Array of storage information per account
  storageAccounts: {
    type: Array as () => StorageAccount[],
    default: () => []
  },
  // Loading state
  isLoading: {
    type: Boolean,
    default: false
  }
});

// Refs for the animated numbers shown in the UI
const animatedPercentage = ref(0);
const animatedUsedBytes = ref(0);
const showBreakdown = ref(false);

// Define emits
const emit = defineEmits(['refresh']);

// --- SVG Donut Chart Calculations ---
const radius = 50;
const circumference = 2 * Math.PI * radius;

const totalQuotaLimitBytes = computed(() => props.totalQuotaLimitGB * 1024 * 1024 * 1024);

const percentage = computed(() => {
  if (totalQuotaLimitBytes.value === 0) return 0;
  return Math.min(100, (props.usedBytes / totalQuotaLimitBytes.value) * 100);
});

// This computed property drives the SVG circle's animation
const strokeOffset = computed(() => {
    return circumference - (animatedPercentage.value / 100) * circumference;
});

// This computed property changes the color of the chart based on usage
const progressColor = computed(() => {
  if (percentage.value >= 90) return '#ef4444'; // Red for critical
  if (percentage.value >= 75) return '#f59e0b'; // Yellow for warning
  return '#4a6cf7'; // Blue for normal
});

const availableBytes = computed(() => {
  return Math.max(0, totalQuotaLimitBytes.value - props.usedBytes);
});

const connectedAccountsCount = computed(() => {
  return props.storageAccounts.filter(account => account.isConnected).length;
});

// --- Utility Functions ---
function formatBytes(bytes: number, decimals = 2): string {
  if (!+bytes) return '0 Bytes';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
}

function toggleBreakdown() {
  showBreakdown.value = !showBreakdown.value;
}

// --- Animation Logic ---
function animateValue(targetRef: any, endValue: number, duration = 750) {
  const startValue = targetRef.value;
  const range = endValue - startValue;
  let startTime: number | null = null;

  function step(currentTime: number) {
    if (!startTime) startTime = currentTime;
    const progress = Math.min((currentTime - startTime) / duration, 1);
    targetRef.value = startValue + range * progress;
    if (progress < 1) {
      requestAnimationFrame(step);
    } else {
      targetRef.value = endValue; // Ensure it ends on the exact value
    }
  }
  requestAnimationFrame(step);
}

// Watch for prop changes to trigger the animations
watch(() => props.usedBytes, (newUsedBytes) => {
    animateValue(animatedUsedBytes, newUsedBytes);
    animateValue(animatedPercentage, percentage.value);
}, { immediate: true }); // `immediate: true` runs the watcher on component mount

</script>

<style scoped>
.dashboard-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.07);
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.card-header h3 {
  color: #1a202c;
  margin: 0;
  font-size: 1.25em;
  font-weight: 600;
}

.header-left svg {
  color: #4a6cf7;
}

.refresh-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #64748b;
}

.refresh-btn:hover:not(:disabled) {
  background: #e2e8f0;
  color: #475569;
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.refresh-btn svg.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.chart-container {
  position: relative;
  margin: 20px 0;
}

.donut-chart {
  width: 160px;
  height: 160px;
  transform: rotate(-90deg); /* Start the circle from the top */
}

.donut-chart-bg,
.donut-chart-fg {
  fill: none;
  stroke-width: 12;
}

.donut-chart-bg {
  stroke: #e2e8f0;
}

.donut-chart-fg {
  stroke-linecap: round; /* Makes the end of the progress line rounded */
  transition: stroke-dashoffset 0.75s ease-out, stroke 0.5s ease;
}

.chart-text-percent {
  font-size: 28px;
  font-weight: 700;
  text-anchor: middle;
  fill: #1a202c;
  transform: rotate(90deg);
  transform-origin: center;
}

.storage-details {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.primary-stats {
    display: flex;
    justify-content: space-between;
    gap: 16px;
}

.used-storage, .total-storage {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.used-storage .value, .total-storage .value {
    font-size: 1.25em;
    font-weight: 600;
    color: #1a202c;
}

.used-storage .label, .total-storage .label {
    font-size: 0.875em;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.storage-summary {
    display: flex;
    justify-content: space-between;
    padding: 12px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.summary-label {
    font-size: 0.75em;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-value {
    font-size: 0.875em;
    font-weight: 600;
    color: #2d3748;
}

.breakdown-toggle {
    display: flex;
    justify-content: center;
}

.toggle-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 0.875em;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.2s ease;
}

.toggle-btn:hover {
    background: #e2e8f0;
    color: #2d3748;
}

.detailed-breakdown {
    border-top: 1px solid #e2e8f0;
    padding-top: 16px;
}

.breakdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.breakdown-header h4 {
    margin: 0;
    font-size: 1em;
    font-weight: 600;
    color: #2d3748;
}

.accounts-breakdown {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 200px;
    overflow-y: auto;
}

.account-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.account-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.account-name {
    font-weight: 500;
    color: #2d3748;
    font-size: 0.875em;
}

.account-status {
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-indicator.connected {
    background-color: #22c55e;
}

.status-indicator.disconnected {
    background-color: #ef4444;
}

.status-text {
    font-size: 0.75em;
    color: #718096;
}

.account-storage {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 2px;
}

.storage-used {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.875em;
}

.storage-quota {
    font-size: 0.75em;
    color: #718096;
}
</style>