<template>
  <div class="account-card" @click="$emit('click', account)">
    <div class="card-icon">
      <slot name="icon">
        <div class="default-icon">📁</div>
      </slot>
    </div>
    <div class="card-content">
      <h3 class="card-title">{{ account.name }}</h3>
      <StatusIndicator :status="account.isConnected" />
    </div>
    <div v-if="account.connectionError" class="connection-error">
      {{ account.connectionError }}
    </div>
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import StatusIndicator from './StatusIndicator.vue';

defineProps({
  account: {
    type: Object,
    required: true,
    default: () => ({
      id: '',
      name: '',
      isConnected: false,
      connectionError: ''
    })
  }
});

defineEmits(['click']);
</script>

<style scoped>
.account-card {
  background: #ffffff;
  border-radius: 14px;
  padding: 25px 20px; /* Increased padding */
  text-align: center;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; /* Center content vertically */
  min-height: 150px; /* Slightly increased min-height */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid #edf2f7;
  cursor: pointer;
  /* Add visual feedback for active/focused state */
  outline: none; /* Remove default outline */
}

.account-card:focus-within {
  border-color: #4a6cf7; /* Highlight border on focus */
  box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.3); /* Add a subtle glow */
}

.account-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.card-icon {
  font-size: 3.5em; /* Slightly larger icon */
  color: #4a6cf7;
  margin-bottom: 12px; /* Increased margin */
}

.default-icon {
  font-size: 3rem; /* Adjusted default icon size */
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px; /* Reduced gap between title and status */
  margin-bottom: 10px; /* Added margin below content */
}

.card-title {
  font-weight: 600;
  font-size: 1.2em; /* Slightly larger title */
  color: #2d3748;
  margin-bottom: 0;
}

.connection-error {
  color: #dc3545;
  font-size: 0.85em;
  margin-top: auto; /* Push error to the bottom */
  padding-top: 8px; /* Add padding above error */
  border-top: 1px solid #edf2f7; /* Add a subtle separator */
  width: 100%; /* Make error span full width */
}
</style>