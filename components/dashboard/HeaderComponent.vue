<template>
  <header class="dashboard-header">
    <div class="header-left">
      <span class="site-title">chinen cloud</span>
    </div>
    <div class="header-right">
      <nav class="main-nav">
        <ul>
          <li><NuxtLink to="/">Dashboard</NuxtLink></li>
          <li><NuxtLink to="/all-files">All Files</NuxtLink></li>
          <li><NuxtLink to="/notifications">Notifications</NuxtLink></li>
          <!-- Add more menu items as needed -->
        </ul>
      </nav>
      <button class="hamburger-menu" @click="toggleSidebar">
        ☰
      </button>
    </div>
  </header>
</template>

<script setup lang="ts">
import { defineEmits } from 'vue';

const emit = defineEmits(['toggle-sidebar']);

const toggleSidebar = () => {
  emit('toggle-sidebar');
};
</script>

<style scoped>
.dashboard-header {
  background-color: #ffffff;
  padding: 15px 30px;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left .site-title {
  font-size: 1.8em;
  font-weight: 800;
  color: #4a6cf7; /* A distinct color for the title */
}

.header-right {
  display: flex;
  align-items: center;
}

.main-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  gap: 20px;
}

.main-nav li {
  display: inline-block;
}

.main-nav a {
  text-decoration: none;
  color: #2d3748;
  font-weight: 500;
  transition: color 0.3s ease;
}

.main-nav a:hover,
.main-nav a.router-link-active {
  color: #4a6cf7;
  font-weight: 700; /* Make active link bolder */
}

.hamburger-menu {
  display: none; /* Hidden by default on larger screens */
  background: none;
  border: none;
  font-size: 1.8em;
  cursor: pointer;
  color: #2d3748;
  margin-left: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-nav {
    display: none; /* Hide main nav on smaller screens */
  }

  .hamburger-menu {
    display: block; /* Show hamburger on smaller screens */
  }
}
</style>