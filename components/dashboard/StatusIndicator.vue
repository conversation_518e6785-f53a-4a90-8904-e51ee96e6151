<template>
  <span class="status-indicator" :class="statusClass" role="status" aria-live="polite">
    
    <span :key="status.toString()" class="icon-wrapper">
      <svg v-if="status" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
        <path d="M20 6 9 17l-5-5"></path>
      </svg>
      <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
        <path d="M18 6 6 18"></path><path d="m6 6 12 12"></path>
      </svg>
    </span>

    <span>{{ statusText }}</span>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  status: {
    type: Boolean,
    required: true
  }
});

// Computed property for the text label
const statusText = computed(() => (props.status ? 'Connected' : 'Disconnected'));

// Computed property for the CSS class
const statusClass = computed(() => (props.status ? 'connected' : 'disconnected'));
</script>

<style scoped>
.status-indicator {
  /* Using inline-flex for better alignment of icon and text */
  display: inline-flex;
  align-items: center;
  gap: 8px; /* Space between icon and text */
  font-size: 0.875rem; /* 14px */
  font-weight: 500;
  padding: 5px 12px;
  border-radius: 9999px; /* Pill shape */
  
  /* Smooth transition for background-color and color changes */
  transition: all 0.3s ease-in-out;
}

.icon-wrapper {
  /* Aligns icon vertically */
  display: flex;
  align-items: center;
}

/* Specific styles for the "connected" state */
.status-indicator.connected {
  background-color: #e6f7ee;
  color: #10b981;
  /* Add a subtle pulsing animation to indicate a "live" connection */
  animation: pulse-green 2s infinite;
}

/* Specific styles for the "disconnected" state */
.status-indicator.disconnected {
  background-color: #fef2f2;
  color: #ef4444;
}

/* Keyframes for the pulsing animation */
@keyframes pulse-green {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(16, 185, 129, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

/* Transition for the icon itself.
  When the icon enters or leaves, it will fade smoothly.
  We are applying this to the icon by targeting its parent, `.icon-wrapper`.
  The transition is triggered by the `v-if` / `v-else` in the template.
*/
.icon-wrapper {
  transition: opacity 0.3s ease-in-out;
}
</style>