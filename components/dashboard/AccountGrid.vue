<template>
  <div class="account-grid">
    <AccountCard
      v-for="account in accounts"
      :key="account.id"
      :account="account"
      @click="handleAccountClick(account)"
    />
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import AccountCard from './AccountCard.vue';
import type { Account } from '~/pages/index.vue';

const props = defineProps({
  accounts: {
    type: Array<Account>,
    required: true,
    default: () => []
  }
});

import { useRouter } from 'vue-router';

const router = useRouter();

const handleAccountClick = (account: any) => {
  router.push({
    path: '/all-files',
    query: { accountId: account.id }
  });
};
</script>

<style scoped>
.account-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 25px;
}
</style>