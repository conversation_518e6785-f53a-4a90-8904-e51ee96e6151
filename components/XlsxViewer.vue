<template>
  <div ref="grid"></div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as XLSX from 'xlsx';

const grid = ref(null);

const workbook = {
  SheetNames: ['Sheet1'],
  Sheets: {
    Sheet1: {
      '!ref': 'A1:B2',
      cells: {
        A1: { v: 'Hello', t: 's', s: { fill: { fgColor: { rgb: 'FF0000' } } } },
        B1: { v: 'World', t: 's' },
        A2: { v: 1, t: 'n' },
        B2: { v: 2, t: 'n' },
      },
    },
  },
};

onMounted(() => {
  const ws = workbook.Sheets.Sheet1;
  const table = document.createElement('table');
  table.id = 'spreadsheet-grid';

  const range = XLSX.utils.decode_range(ws['!ref']);

  for (let R = range.s.r; R <= range.e.r; ++R) {
    const tr = table.insertRow(-1);
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const td = tr.insertCell(-1);
      const cell_ref = XLSX.utils.encode_cell({ c: C, r: R });
      const cell = ws.cells[cell_ref];

      if (cell) {
        td.innerHTML = cell.w || cell.v || '';
        if (cell.s) {
          let style = '';
          if (cell.s.fill && cell.s.fill.fgColor && cell.s.fill.fgColor.rgb) {
            const bgColor = ('000000' + cell.s.fill.fgColor.rgb).slice(-6);
            style += `background-color: #${bgColor};`;
          }
          if (style) td.setAttribute('style', style);
        }
      }
    }
  }
  grid.value.appendChild(table);
});
</script>