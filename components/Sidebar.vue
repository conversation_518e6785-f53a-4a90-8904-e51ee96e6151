<template>
  <div class="sidebar" :class="{ 'is-open': isOpen }">
    <div class="sidebar-header">
      <h3>Menu</h3>
      <button class="close-button" @click="$emit('close')">×</button>
    </div>
    <nav class="sidebar-nav">
      <ul>
        <li><NuxtLink to="/" @click="$emit('close')">Dashboard</NuxtLink></li>
        <li><NuxtLink to="/all-files" @click="$emit('close')">All Files</NuxtLink></li>
        <li><NuxtLink to="/notifications" @click="$emit('close')">Notifications</NuxtLink></li>
        <!-- Add more sidebar menu items -->
      </ul>
    </nav>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';

defineProps({
  isOpen: {
    type: Boolean,
    required: true,
    default: false
  }
});

defineEmits(['close']);
</script>

<style scoped>
.sidebar {
  position: fixed;
  top: 0;
  left: -250px; /* Start off-screen */
  width: 250px;
  height: 100%;
  background-color: #ffffff;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  transition: left 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.sidebar.is-open {
  left: 0; /* Slide in */
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e2e8f0;
}

.sidebar-header h3 {
  margin: 0;
  color: #2d3748;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5em;
  cursor: pointer;
  color: #2d3748;
}

.sidebar-nav ul {
  list-style: none;
  padding: 20px;
  margin: 0;
}

.sidebar-nav li {
  margin-bottom: 15px;
}

.sidebar-nav a {
  text-decoration: none;
  color: #2d3748;
  font-size: 1.1em;
  font-weight: 500;
  transition: color 0.3s ease;
}

.sidebar-nav a:hover,
.sidebar-nav a.router-link-active {
  color: #4a6cf7;
  font-weight: 700; /* Make active link bolder */
}
</style>