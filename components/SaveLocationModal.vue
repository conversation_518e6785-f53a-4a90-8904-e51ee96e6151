<template>
  <div v-if="show" class="modal-overlay" @click.self="$emit('close')">
    <div class="modal-content save-location-modal">
      <button class="modal-close" @click="$emit('close')">&times;</button>
      <h2 class="modal-title">Choose Save Location</h2>
      
      <div class="save-form">
        <!-- File Name Input -->
        <div class="form-group">
          <label for="fileName" class="form-label">File Name</label>
          <div class="file-name-input-group">
            <input
              id="fileName"
              v-model="localFileName"
              type="text"
              class="form-input"
              placeholder="Enter file name"
              @input="validateFileName"
            />
            <select v-model="selectedFormat" class="format-select">
              <option v-for="format in availableFormats" :key="format.value" :value="format.value">
                {{ format.label }}
              </option>
            </select>
          </div>
          <div v-if="fileNameError" class="error-message">{{ fileNameError }}</div>
        </div>

        <!-- Account Selection -->
        <div class="form-group">
          <label for="account" class="form-label">Account</label>
          <select
            id="account"
            v-model="selectedAccount"
            class="form-input"
            @change="loadFolders"
          >
            <option value="">Select an account</option>
            <option v-for="account in accounts" :key="account.id" :value="account">
              {{ account.name }}
            </option>
          </select>
        </div>

        <!-- Folder Navigation -->
        <div v-if="selectedAccount" class="form-group">
          <label class="form-label">Folder Location</label>
          
          <!-- Breadcrumb Navigation -->
          <div class="breadcrumb-nav">
            <button
              @click="navigateToFolder('/')"
              class="breadcrumb-item"
              :class="{ active: currentPath === '/' }"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z" />
              </svg>
              Root
            </button>
            <span v-for="(folder, index) in pathSegments" :key="index" class="breadcrumb-separator">/</span>
            <button
              v-for="(folder, index) in pathSegments"
              :key="index"
              @click="navigateToFolder(getPathUpTo(index))"
              class="breadcrumb-item"
              :class="{ active: index === pathSegments.length - 1 }"
            >
              {{ folder }}
            </button>
          </div>

          <!-- Folder List -->
          <div class="folder-list">
            <div v-if="loadingFolders" class="loading-state">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="spinner">
                <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z" />
              </svg>
              Loading folders...
            </div>
            
            <div v-else-if="folders.length === 0" class="empty-state">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M10,4L12,6H20A2,2 0 0,1 22,8V18A2,2 0 0,1 20,20H4C1.8,20 2,18 2,18V6C2,4.89 2.9,4 4,4H10Z" />
              </svg>
              No folders in this location
            </div>
            
            <div v-else class="folder-grid">
              <button
                v-for="folder in folders"
                :key="folder.path"
                @click="navigateToFolder(folder.path)"
                class="folder-item"
                :class="{ selected: selectedFolder === folder.path }"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M10,4L12,6H20A2,2 0 0,1 22,8V18A2,2 0 0,1 20,20H4C1.8,20 2,18 2,18V6C2,4.89 2.9,4 4,4H10Z" />
                </svg>
                <span class="folder-name">{{ folder.name }}</span>
              </button>
            </div>
          </div>

          <!-- Create New Folder -->
          <div class="new-folder-section">
            <div v-if="showNewFolderInput" class="new-folder-input">
              <input
                v-model="newFolderName"
                type="text"
                placeholder="Folder name"
                class="form-input"
                @keyup.enter="createNewFolder"
                @keyup.escape="cancelNewFolder"
              />
              <button @click="createNewFolder" class="btn btn-primary btn-sm">Create</button>
              <button @click="cancelNewFolder" class="btn btn-secondary btn-sm">Cancel</button>
            </div>
            <button v-else @click="showNewFolderInput = true" class="btn btn-outline">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M10,4L12,6H20A2,2 0 0,1 22,8V18A2,2 0 0,1 20,20H4C1.8,20 2,18 2,18V6C2,4.89 2.9,4 4,4H10M14,12H16V14H19V16H16V19H14V16H11V14H14V12Z" />
              </svg>
              New Folder
            </button>
          </div>
        </div>

        <!-- Save Actions -->
        <div class="form-actions">
          <button @click="$emit('close')" class="btn btn-secondary">Cancel</button>
          <button
            @click="handleSave"
            :disabled="!canSave || saving"
            class="btn btn-primary"
          >
            <svg v-if="saving" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="spinner">
              <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z" />
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
              <path d="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z" />
            </svg>
            {{ saving ? 'Saving...' : 'Save' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  fileName: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: 'document'
  },
  accounts: {
    type: Array,
    default: () => []
  },
  defaultAccount: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['close', 'save']);

// Form state
const localFileName = ref('');
const selectedAccount = ref(null);
const selectedFormat = ref('docx');
const currentPath = ref('/');
const selectedFolder = ref('/');
const folders = ref([]);
const loadingFolders = ref(false);
const saving = ref(false);
const fileNameError = ref('');

// New folder creation
const showNewFolderInput = ref(false);
const newFolderName = ref('');

// Available formats based on file type
const availableFormats = computed(() => {
  const formatMap = {
    document: [
      { value: 'docx', label: '.docx (Word Document)' },
      { value: 'html', label: '.html (Web Page)' },
      { value: 'txt', label: '.txt (Plain Text)' },
      { value: 'md', label: '.md (Markdown)' }
    ],
    spreadsheet: [
      { value: 'xlsx', label: '.xlsx (Excel Workbook)' },
      { value: 'csv', label: '.csv (Comma Separated)' }
    ],
    text: [
      { value: 'txt', label: '.txt (Plain Text)' },
      { value: 'md', label: '.md (Markdown)' },
      { value: 'html', label: '.html (Web Page)' }
    ]
  };
  return formatMap[props.fileType] || formatMap.document;
});

// Computed properties
const pathSegments = computed(() => {
  return currentPath.value === '/' ? [] : currentPath.value.split('/').filter(Boolean);
});

const canSave = computed(() => {
  return localFileName.value.trim() && selectedAccount.value && !fileNameError.value;
});

// Methods
const validateFileName = () => {
  const name = localFileName.value.trim();
  if (!name) {
    fileNameError.value = 'File name is required';
    return;
  }
  if (name.includes('/') || name.includes('\\')) {
    fileNameError.value = 'File name cannot contain / or \\';
    return;
  }
  if (name.length > 255) {
    fileNameError.value = 'File name is too long';
    return;
  }
  fileNameError.value = '';
};

const loadFolders = async () => {
  if (!selectedAccount.value) return;
  
  loadingFolders.value = true;
  try {
    const response = await fetch(`/api/files/${selectedAccount.value.id}?path=${encodeURIComponent(currentPath.value)}`);
    const data = await response.json();
    
    if (data.error) {
      throw new Error(data.error);
    }
    
    folders.value = data.files.filter(item => item.type === 'directory');
  } catch (error) {
    console.error('Error loading folders:', error);
    folders.value = [];
  } finally {
    loadingFolders.value = false;
  }
};

const navigateToFolder = (path) => {
  currentPath.value = path;
  selectedFolder.value = path;
  loadFolders();
};

const getPathUpTo = (index) => {
  const segments = pathSegments.value.slice(0, index + 1);
  return '/' + segments.join('/');
};

const createNewFolder = async () => {
  if (!newFolderName.value.trim()) return;
  
  try {
    const folderPath = currentPath.value === '/' 
      ? `/${newFolderName.value}` 
      : `${currentPath.value}/${newFolderName.value}`;
    
    // Create folder via API (you'll need to implement this endpoint)
    const response = await fetch(`/api/create-folder/${selectedAccount.value.id}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ path: folderPath })
    });
    
    if (response.ok) {
      await loadFolders();
      cancelNewFolder();
    }
  } catch (error) {
    console.error('Error creating folder:', error);
  }
};

const cancelNewFolder = () => {
  showNewFolderInput.value = false;
  newFolderName.value = '';
};

const handleSave = () => {
  if (!canSave.value) return;
  
  saving.value = true;
  const saveData = {
    fileName: localFileName.value.trim(),
    format: selectedFormat.value,
    account: selectedAccount.value,
    path: currentPath.value,
    fullPath: currentPath.value === '/' 
      ? `/${localFileName.value}.${selectedFormat.value}`
      : `${currentPath.value}/${localFileName.value}.${selectedFormat.value}`
  };
  
  emit('save', saveData);
};

// Watchers
watch(() => props.show, (newVal) => {
  if (newVal) {
    localFileName.value = props.fileName || '';
    selectedAccount.value = props.defaultAccount;
    currentPath.value = '/';
    selectedFolder.value = '/';
    saving.value = false;
    fileNameError.value = '';
    
    if (selectedAccount.value) {
      loadFolders();
    }
  }
});

watch(() => props.fileName, (newVal) => {
  if (newVal) {
    localFileName.value = newVal;
  }
});

// Expose method to parent
defineExpose({
  setSaving: (value) => {
    saving.value = value;
  }
});
</script>

<style scoped>
.save-location-modal {
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-title {
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.save-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.file-name-input-group {
  display: flex;
  gap: 0.5rem;
}

.file-name-input-group .form-input {
  flex: 1;
}

.format-select {
  min-width: 200px;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background: white;
  font-size: 0.875rem;
}

.form-input {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.breadcrumb-nav {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: none;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.875rem;
  color: #6b7280;
  transition: all 0.2s;
}

.breadcrumb-item:hover {
  background: #e5e7eb;
  color: #374151;
}

.breadcrumb-item.active {
  color: #1f2937;
  font-weight: 500;
}

.breadcrumb-item svg {
  width: 1rem;
  height: 1rem;
}

.breadcrumb-separator {
  color: #9ca3af;
  font-size: 0.875rem;
}

.folder-list {
  min-height: 200px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  padding: 1rem;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  height: 150px;
  color: #6b7280;
  font-size: 0.875rem;
}

.loading-state svg,
.empty-state svg {
  width: 2rem;
  height: 2rem;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.folder-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.75rem;
}

.folder-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
}

.folder-item:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.folder-item.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.folder-item svg {
  width: 2rem;
  height: 2rem;
  color: #3b82f6;
}

.folder-name {
  font-size: 0.75rem;
  color: #374151;
  word-break: break-word;
}

.new-folder-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.new-folder-input {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.new-folder-input .form-input {
  flex: 1;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn svg {
  width: 1rem;
  height: 1rem;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #4b5563;
}

.btn-outline {
  border-color: #d1d5db;
  color: #374151;
  background: white;
}

.btn-outline:hover {
  border-color: #9ca3af;
  background: #f9fafb;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 2rem;
  border-radius: 0.75rem;
  position: relative;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  transition: all 0.2s;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}
</style>
