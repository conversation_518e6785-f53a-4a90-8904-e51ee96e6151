<template>
  <div v-if="isVisible" class="modal-overlay" @click.self="close">
    <div class="modal-content">
      <button class="modal-close-btn" @click="close">×</button>
      <h2 class="modal-header">Preview: {{ imageName }}</h2>
      <div v-if="isLoading" class="state-cell">Loading preview...</div>
      <div v-else-if="error" class="state-cell error">Error loading preview: {{ error }}</div>
      <div v-else class="modal-body">
        <div
          class="image-preview-container"
          ref="imageContainer"
          @mousedown="startDrag"
          @mousemove="doDrag"
          @mouseup="endDrag"
          @mouseleave="endDrag"
          @wheel.prevent="handleScrollZoom"
          :class="{ dragging: isDragging, drawing: isDrawing }"
        >
          <img
            :src="imageBlobUrl"
            alt="File Preview"
            class="preview-media"
            ref="imageElement"
            @load="resetAndFitImage"
            :style="{ transform: `scale(${scale}) translate(${translateX}px, ${translateY}px)` }"
          />
          <canvas
            ref="canvas"
            class="drawing-canvas"
            :width="containerWidth"
            :height="containerHeight"
            @mousedown="startDrawing"
            @mousemove="draw"
            @mouseup="stopDrawing"
            @mouseleave="stopDrawing"
          ></canvas>
          <div class="zoom-controls">
            <button @click="zoomIn" title="Zoom In"><i class="fas fa-plus"></i></button>
            <button @click="zoomOut" title="Zoom Out"><i class="fas fa-minus"></i></button>
          </div>
          <div class="drawing-tools">
            <button @click="toggleDrawingMode" title="Draw" :class="{ active: drawingMode === 'draw' }"><i class="fas fa-pencil-alt"></i></button>
            <button @click="toggleEraserMode" title="Erase" :class="{ active: drawingMode === 'erase' }"><i class="fas fa-eraser"></i></button>
            <input type="color" v-model="drawingColor" title="Choose Color" />
            <button @click="clearCanvas" title="Clear Canvas"><i class="fas fa-trash-alt"></i></button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  imageUrl: {
    type: String,
    required: true,
  },
  imageName: {
    type: String,
    default: 'Image',
  },
});

const emit = defineEmits(['close']);

const isVisible = ref(false);
const isLoading = ref(false);
const error = ref(null);
const imageBlobUrl = ref('');

const scale = ref(1);
const translateX = ref(0);
const translateY = ref(0);
const isDragging = ref(false);
const lastDragX = ref(0);
const lastDragY = ref(0);
const imageContainer = ref(null);
const imageElement = ref(null);
const containerWidth = ref(0);
const containerHeight = ref(0);
const imageWidth = ref(0);
const imageHeight = ref(0);

// Drawing functionality
const canvas = ref(null);
const ctx = ref(null);
const isDrawing = ref(false);
const lastDrawX = ref(0);
const lastDrawY = ref(0);
const drawingColor = ref('#000000');
const drawingMode = ref('none'); // 'draw', 'erase', or 'none'

const close = () => {
  emit('close');
};

const fetchImage = async (url) => {
  if (!url) return;
  isLoading.value = true;
  error.value = null;
  if (imageBlobUrl.value) {
    URL.revokeObjectURL(imageBlobUrl.value);
    imageBlobUrl.value = '';
  }
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`Failed to load image (status: ${response.status})`);
    const blob = await response.blob();
    imageBlobUrl.value = URL.createObjectURL(blob);
  } catch (e) {
    console.error(e);
    error.value = e.message;
  } finally {
    isLoading.value = false;
  }
};

watch(() => props.show, (newVal) => {
  isVisible.value = newVal;
  if (newVal) {
    nextTick(() => {
      if(imageContainer.value) {
        updateDimensions();
        initCanvas();
      }
    });
    fetchImage(props.imageUrl);
  } else {
    if (imageBlobUrl.value) {
      URL.revokeObjectURL(imageBlobUrl.value);
      imageBlobUrl.value = '';
    }
  }
});

onUnmounted(() => {
  if (imageBlobUrl.value) {
    URL.revokeObjectURL(imageBlobUrl.value);
  }
});

const initCanvas = () => {
  if (!canvas.value) return;
  ctx.value = canvas.value.getContext('2d');
  ctx.value.strokeStyle = drawingColor.value;
  ctx.value.lineWidth = 2;
  ctx.value.lineCap = 'round';
  ctx.value.lineJoin = 'round';
};

const resetAndFitImage = () => {
  scale.value = 1;
  translateX.value = 0;
  translateY.value = 0;
  nextTick(() => {
    updateDimensions();
    initCanvas();
  });
};

const updateDimensions = () => {
  if (!imageContainer.value || !imageElement.value || imageElement.value.clientWidth === 0) {
    return;
  }
  // Get container dimensions
  containerWidth.value = imageContainer.value.clientWidth;
  containerHeight.value = imageContainer.value.clientHeight;

  // Get the image's actual rendered dimensions at scale: 1
  imageWidth.value = imageElement.value.clientWidth;
  imageHeight.value = imageElement.value.clientHeight;

  clampCurrentTranslation();
};

const clampTranslation = (currentTranslate, maxAllowedTranslate) => {
  if (maxAllowedTranslate < 0) return 0;
  return Math.max(Math.min(currentTranslate, maxAllowedTranslate), -maxAllowedTranslate);
};

const getClampingLimits = (currentScale) => {
  if (currentScale <= 1 || !imageWidth.value || !containerWidth.value) return { x: 0, y: 0 };

  const scaledImageWidth = imageWidth.value * currentScale;
  const scaledImageHeight = imageHeight.value * currentScale;

  const xOverflow = scaledImageWidth - containerWidth.value;
  const yOverflow = scaledImageHeight - containerHeight.value;

  const maxTranslateX = Math.max(0, xOverflow / (2 * currentScale));
  const maxTranslateY = Math.max(0, yOverflow / (2 * currentScale));

  return { x: maxTranslateX, y: maxTranslateY };
};

const clampCurrentTranslation = () => {
  const { x: maxTranslateX, y: maxTranslateY } = getClampingLimits(scale.value);
  translateX.value = clampTranslation(translateX.value, maxTranslateX);
  translateY.value = clampTranslation(translateY.value, maxTranslateY);
};

const zoomIn = () => {
  scale.value = Math.min(scale.value + 0.2, 5);
  clampCurrentTranslation();
};

const zoomOut = () => {
  scale.value = Math.max(scale.value - 0.2, 0.5);
  clampCurrentTranslation();
};

const handleScrollZoom = (event) => {
  if (!imageContainer.value) return;

  const oldScale = scale.value;
  const zoomFactor = 1.1;
  let newScale;

  if (event.deltaY < 0) {
    newScale = Math.min(oldScale * zoomFactor, 5);
  } else {
    newScale = Math.max(oldScale / zoomFactor, 0.5);
  }

  if (newScale === oldScale) return;

  const rect = imageContainer.value.getBoundingClientRect();
  const mouseX = event.clientX - rect.left;
  const mouseY = event.clientY - rect.top;

  const mouseRelativeToCenterX = mouseX - containerWidth.value / 2;
  const mouseRelativeToCenterY = mouseY - containerHeight.value / 2;

  const newTranslateX = translateX.value - (mouseRelativeToCenterX / oldScale) * (1 - oldScale / newScale);
  const newTranslateY = translateY.value - (mouseRelativeToCenterY / oldScale) * (1 - oldScale / newScale);

  scale.value = newScale;

  const { x: maxTranslateX, y: maxTranslateY } = getClampingLimits(newScale);
  translateX.value = clampTranslation(newTranslateX, maxTranslateX);
  translateY.value = clampTranslation(newTranslateY, maxTranslateY);
};

const startDrag = (event) => {
  if (scale.value > 1 && drawingMode.value === 'none') {
    isDragging.value = true;
    lastDragX.value = event.clientX;
    lastDragY.value = event.clientY;
    event.preventDefault();
  }
};

const doDrag = (event) => {
  if (isDragging.value) {
    const deltaX = event.clientX - lastDragX.value;
    const deltaY = event.clientY - lastDragY.value;
    lastDragX.value = event.clientX;
    lastDragY.value = event.clientY;

    const newTranslateX = translateX.value + deltaX / scale.value;
    const newTranslateY = translateY.value + deltaY / scale.value;

    const { x: maxTranslateX, y: maxTranslateY } = getClampingLimits(scale.value);
    translateX.value = clampTranslation(newTranslateX, maxTranslateX);
    translateY.value = clampTranslation(newTranslateY, maxTranslateY);
  }
};

const endDrag = () => {
  isDragging.value = false;
};

const startDrawing = (event) => {
  if (drawingMode.value === 'draw' || drawingMode.value === 'erase') {
    isDrawing.value = true;
    lastDrawX.value = event.offsetX;
    lastDrawY.value = event.offsetY;
  }
};

const draw = (event) => {
  if (!isDrawing.value) return;
  const currentX = event.offsetX;
  const currentY = event.offsetY;

  ctx.value.beginPath();
  ctx.value.moveTo(lastDrawX.value, lastDrawY.value);
  ctx.value.lineTo(currentX, currentY);

  if (drawingMode.value === 'draw') {
    ctx.value.strokeStyle = drawingColor.value;
    ctx.value.lineWidth = 2;
  } else if (drawingMode.value === 'erase') {
    ctx.value.strokeStyle = 'white';
    ctx.value.lineWidth = 10;
  }

  ctx.value.stroke();

  lastDrawX.value = currentX;
  lastDrawY.value = currentY;
};

const stopDrawing = () => {
  isDrawing.value = false;
};

const toggleDrawingMode = () => {
  drawingMode.value = drawingMode.value === 'draw' ? 'none' : 'draw';
};

const toggleEraserMode = () => {
  drawingMode.value = drawingMode.value === 'erase' ? 'none' : 'erase';
};

const clearCanvas = () => {
  if (ctx.value) {
    ctx.value.clearRect(0, 0, canvas.value.width, canvas.value.height);
  }
};

onMounted(() => {
  // Dynamically add Font Awesome CSS
  const link = document.createElement('link');
  link.rel = 'stylesheet';
  link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css';
  document.head.appendChild(link);

  const observer = new ResizeObserver(() => {
    if (isVisible.value) {
      updateDimensions();
      initCanvas();
    }
  });
  observer.observe(document.body);
  onUnmounted(() => {
    observer.disconnect();
    // Remove Font Awesome CSS when component is unmounted
    document.head.removeChild(link);
  });
});
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.modal-content {
  background-color: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  max-width: 90vw;
  max-height: 90vh;
  position: relative;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

.modal-close-btn {
  position: absolute;
  top: 0.5rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
  z-index: 10;
  transition: color 0.2s;
}

.modal-close-btn:hover {
  color: #343a40;
}

.modal-header {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-right: 2rem;
  color: #343a40;
}

.modal-body {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.image-preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  cursor: grab;
  transition: all 0.3s ease;
}

.image-preview-container.dragging {
  cursor: grabbing;
}

.image-preview-container.drawing {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 19l7-7 3 3-7 7-3-3z"></path><path d="M18 13l-1.5-7.5L3 3l-3 3L12 20l3-3z"></path></svg>'), auto;
}

.preview-media {
  max-width: 100%;
  max-height: 100%;
  display: block;
  object-fit: contain;
  transition: transform 0.1s ease-out;
  will-change: transform;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.drawing-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: auto;
}

.zoom-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  padding: 10px;
  display: flex;
  gap: 10px;
  z-index: 10;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.zoom-controls button {
  background-color: #0d6efd;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 1.2rem;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.2s, transform 0.2s;
}

.zoom-controls button:hover {
  background-color: #0b5ed7;
  transform: scale(1.1);
}

.drawing-tools {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  padding: 10px;
  display: flex;
  gap: 10px;
  z-index: 10;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.drawing-tools button {
  background-color: #0d6efd;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 0.8rem;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.2s, transform 0.2s;
}

.drawing-tools button:hover {
  background-color: #0b5ed7;
  transform: scale(1.1);
}

.drawing-tools button.active {
  background-color: #dc3545;
  transform: scale(1.1);
}

.drawing-tools input[type="color"] {
  width: 40px;
  height: 40px;
  padding: 0;
  border: none;
  cursor: pointer;
  border-radius: 50%;
  transition: transform 0.2s;
}

.drawing-tools input[type="color"]:hover {
  transform: scale(1.1);
}

.state-cell {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
  font-size: 1.2rem;
}

.state-cell.error {
  color: #dc3545;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
