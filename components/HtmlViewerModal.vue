<template>
  <div v-if="show" class="modal-overlay" @click.self="$emit('close')">
    <div class="modal-content">
      <button class="modal-close" @click="$emit('close')">&times;</button>
      <div class="html-viewer" v-html="htmlContent"></div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  htmlContent: {
    type: String,
    default: ''
  },
  show: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close']);
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  max-width: 90%;
  max-height: 90%;
  overflow: auto;
  position: relative;
}

.modal-close {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
}

.html-viewer {
  /* Basic styling for the HTML content */
  padding: 10px;
}

/* Add some basic styling for tables within the HTML viewer */
.html-viewer table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1em;
}

.html-viewer th,
.html-viewer td {
  border: 1px solid #ccc;
  padding: 8px;
  text-align: left;
}

.html-viewer th {
  background-color: #f2f2f2;
}
</style>