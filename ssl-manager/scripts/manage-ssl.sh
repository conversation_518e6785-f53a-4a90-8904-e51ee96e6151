#!/bin/bash

# SSL Certificate Management Script
# Provides easy commands to manage SSL certificates

set -e

DOMAIN="webmirai.duckdns.org"
EMAIL="<EMAIL>"

show_help() {
    echo "🔐 SSL Certificate Manager"
    echo "========================="
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  init      Initialize SSL certificates for the first time"
    echo "  renew     Force renewal of SSL certificates"
    echo "  status    Check certificate status and expiration"
    echo "  logs      Show recent logs"
    echo "  start     Start SSL services"
    echo "  stop      Stop SSL services"
    echo "  restart   Restart SSL services"
    echo "  test      Test SSL configuration"
    echo "  help      Show this help message"
    echo ""
}

init_ssl() {
    echo "🔐 Initializing SSL certificates..."
    ./scripts/init-ssl.sh
}

renew_ssl() {
    echo "🔄 Renewing SSL certificates..."
    docker-compose run --rm certbot certonly --webroot \
        --webroot-path=/var/www/certbot \
        --email $EMAIL \
        --agree-tos \
        --no-eff-email \
        --force-renewal \
        --non-interactive \
        -d $DOMAIN
    
    echo "🔄 Reloading nginx..."
    docker-compose exec nginx-ssl nginx -s reload
    echo "✅ Certificate renewal completed"
}

check_status() {
    echo "📊 SSL Certificate Status"
    echo "========================"
    
    if docker-compose exec nginx-ssl test -f /etc/letsencrypt/live/$DOMAIN/fullchain.pem; then
        echo "✅ Certificate exists for $DOMAIN"
        
        # Check expiration
        echo "📅 Certificate expiration:"
        docker-compose exec nginx-ssl openssl x509 -in /etc/letsencrypt/live/$DOMAIN/fullchain.pem -noout -dates
        
        # Check certificate details
        echo ""
        echo "🔍 Certificate details:"
        docker-compose exec nginx-ssl openssl x509 -in /etc/letsencrypt/live/$DOMAIN/fullchain.pem -noout -subject -issuer
    else
        echo "❌ No certificate found for $DOMAIN"
    fi
    
    echo ""
    echo "🐳 Service status:"
    docker-compose ps
}

show_logs() {
    echo "📋 Recent SSL logs:"
    echo "=================="
    echo ""
    echo "🔍 Nginx logs:"
    docker-compose logs --tail=50 nginx-ssl
    echo ""
    echo "🔍 Certbot logs:"
    docker-compose logs --tail=50 certbot
}

start_services() {
    echo "🚀 Starting SSL services..."
    docker-compose up -d
    echo "✅ Services started"
}

stop_services() {
    echo "🛑 Stopping SSL services..."
    docker-compose down
    echo "✅ Services stopped"
}

restart_services() {
    echo "🔄 Restarting SSL services..."
    docker-compose restart
    echo "✅ Services restarted"
}

test_ssl() {
    echo "🧪 Testing SSL configuration..."
    echo "=============================="
    
    # Test nginx configuration
    echo "🔍 Testing nginx configuration..."
    if docker-compose exec nginx-ssl nginx -t; then
        echo "✅ Nginx configuration is valid"
    else
        echo "❌ Nginx configuration has errors"
        return 1
    fi
    
    # Test HTTP redirect
    echo ""
    echo "🔍 Testing HTTP to HTTPS redirect..."
    if curl -I -s http://localhost/health | grep -q "301"; then
        echo "✅ HTTP redirect is working"
    else
        echo "⚠️  HTTP redirect may not be working"
    fi
    
    # Test HTTPS
    echo ""
    echo "🔍 Testing HTTPS connection..."
    if curl -f -k -s https://localhost/health > /dev/null; then
        echo "✅ HTTPS is working"
    else
        echo "❌ HTTPS is not working"
    fi
    
    echo ""
    echo "🌐 External SSL test:"
    echo "Visit: https://www.ssllabs.com/ssltest/analyze.html?d=$DOMAIN"
}

# Main script logic
case "${1:-help}" in
    init)
        init_ssl
        ;;
    renew)
        renew_ssl
        ;;
    status)
        check_status
        ;;
    logs)
        show_logs
        ;;
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    test)
        test_ssl
        ;;
    help|*)
        show_help
        ;;
esac
