#!/bin/bash

# SSL Certificate Initialization Script
# This script sets up SSL certificates for the first time

set -e

DOMAIN="webmirai.duckdns.org"
EMAIL="<EMAIL>"
COMPOSE_FILE="docker-compose.yml"

echo "🔐 SSL Certificate Initialization for $DOMAIN"
echo "================================================"

# Check if Docker and Docker Compose are available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not installed or not in PATH"
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p nginx/logs
mkdir -p certbot/logs

# Check if certificates already exist
if [ -d "$(docker-compose exec certbot ls /etc/letsencrypt/live/$DOMAIN 2>/dev/null)" ]; then
    echo "⚠️  Certificates for $DOMAIN already exist"
    read -p "Do you want to force renewal? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "ℹ️  Skipping certificate generation"
        exit 0
    fi
fi

# Start nginx first (without SSL)
echo "🚀 Starting nginx for ACME challenge..."
docker-compose up -d nginx-ssl

# Wait for nginx to be ready
echo "⏳ Waiting for nginx to be ready..."
sleep 10

# Test if nginx is responding
if ! curl -f http://localhost/health &> /dev/null; then
    echo "❌ Nginx is not responding on port 80"
    echo "🔍 Checking nginx logs..."
    docker-compose logs nginx-ssl
    exit 1
fi

echo "✅ Nginx is ready"

# Generate certificates
echo "🔒 Generating SSL certificates..."
docker-compose run --rm certbot-init

# Check if certificates were generated successfully
if docker-compose exec nginx-ssl test -f /etc/letsencrypt/live/$DOMAIN/fullchain.pem; then
    echo "✅ SSL certificates generated successfully"
    
    # Reload nginx to use the new certificates
    echo "🔄 Reloading nginx with SSL configuration..."
    docker-compose exec nginx-ssl nginx -s reload
    
    echo "🎉 SSL setup completed successfully!"
    echo "🌐 Your site should now be available at: https://$DOMAIN"
    
    # Test HTTPS
    echo "🧪 Testing HTTPS connection..."
    if curl -f -k https://localhost/health &> /dev/null; then
        echo "✅ HTTPS is working correctly"
    else
        echo "⚠️  HTTPS test failed, but certificates are installed"
        echo "🔍 Check nginx logs for details:"
        docker-compose logs nginx-ssl
    fi
    
else
    echo "❌ Failed to generate SSL certificates"
    echo "🔍 Checking certbot logs..."
    docker-compose logs certbot-init
    exit 1
fi

echo ""
echo "📋 Next steps:"
echo "1. Update your DNS to point $DOMAIN to this server's IP"
echo "2. Test your site: https://$DOMAIN"
echo "3. Certificates will auto-renew every 12 hours"
echo ""
echo "🛠️  Useful commands:"
echo "  - View logs: docker-compose logs"
echo "  - Restart services: docker-compose restart"
echo "  - Stop services: docker-compose down"
