# SSL Certificate Manager

A standalone SSL certificate management system using Docker Compose, Nginx, and Let's Encrypt (Certbot).

## Features

- 🔐 Automatic SSL certificate generation with Let's Encrypt
- 🔄 Automatic certificate renewal every 12 hours
- 🛡️ Modern SSL security configuration
- 🚀 Easy setup and management scripts
- 📊 Certificate status monitoring
- 🧪 Built-in testing tools

## Quick Start

### 1. Initial Setup

```bash
cd ssl-manager
./scripts/init-ssl.sh
```

This will:
- Start nginx for ACME challenge
- Generate SSL certificates for your domain
- Configure automatic renewal

### 2. Management Commands

Use the management script for easy operations:

```bash
# Show all available commands
./scripts/manage-ssl.sh help

# Check certificate status
./scripts/manage-ssl.sh status

# Renew certificates manually
./scripts/manage-ssl.sh renew

# Test SSL configuration
./scripts/manage-ssl.sh test

# View logs
./scripts/manage-ssl.sh logs

# Start/stop/restart services
./scripts/manage-ssl.sh start
./scripts/manage-ssl.sh stop
./scripts/manage-ssl.sh restart
```

## Configuration

### Domain and Email

Edit these variables in the scripts:
- `DOMAIN="webmirai.duckdns.org"`
- `EMAIL="<EMAIL>"`

### Application Backend

The nginx configuration proxies to your application at `http://host.docker.internal:3000`. 

To change this, edit `nginx/conf.d/default.conf`:

```nginx
# Change this line to point to your application
proxy_pass http://your-app-host:your-app-port;
```

## Directory Structure

```
ssl-manager/
├── docker-compose.yml          # Main Docker Compose configuration
├── nginx/
│   ├── conf.d/
│   │   └── default.conf        # Nginx virtual host configuration
│   ├── ssl-params.conf         # SSL security parameters
│   └── logs/                   # Nginx logs
├── certbot/
│   └── logs/                   # Certbot logs
└── scripts/
    ├── init-ssl.sh             # Initial SSL setup
    └── manage-ssl.sh           # Management commands
```

## Services

### nginx-ssl
- **Purpose**: Reverse proxy with SSL termination
- **Ports**: 80 (HTTP), 443 (HTTPS)
- **Features**: 
  - HTTP to HTTPS redirect
  - ACME challenge handling
  - Security headers
  - WebSocket support

### certbot
- **Purpose**: Automatic certificate renewal
- **Schedule**: Every 12 hours
- **Features**:
  - Webroot authentication
  - Automatic renewal
  - Logging

### certbot-init
- **Purpose**: Initial certificate generation
- **Usage**: Run once during setup
- **Profile**: `init` (only runs when explicitly called)

## SSL Security Features

- **Protocols**: TLS 1.2 and 1.3 only
- **Ciphers**: Modern, secure cipher suites
- **HSTS**: Strict Transport Security enabled
- **OCSP Stapling**: Enabled for better performance
- **Security Headers**: 
  - X-Frame-Options: DENY
  - X-Content-Type-Options: nosniff
  - X-XSS-Protection: enabled
  - Referrer-Policy: strict-origin-when-cross-origin

## Troubleshooting

### Certificate Generation Failed

1. Check if your domain points to this server:
   ```bash
   nslookup webmirai.duckdns.org
   ```

2. Ensure ports 80 and 443 are accessible:
   ```bash
   curl -I http://your-domain/.well-known/acme-challenge/test
   ```

3. Check certbot logs:
   ```bash
   ./scripts/manage-ssl.sh logs
   ```

### Nginx Configuration Issues

1. Test nginx configuration:
   ```bash
   ./scripts/manage-ssl.sh test
   ```

2. Check nginx logs:
   ```bash
   docker-compose logs nginx-ssl
   ```

### Certificate Renewal Issues

1. Test renewal manually:
   ```bash
   ./scripts/manage-ssl.sh renew
   ```

2. Check certificate expiration:
   ```bash
   ./scripts/manage-ssl.sh status
   ```

## Integration with Your Application

### Option 1: External Network (Recommended)

If your application runs in a separate Docker Compose setup:

1. Create a shared network:
   ```bash
   docker network create shared-network
   ```

2. Add to your application's docker-compose.yml:
   ```yaml
   networks:
     default:
       external:
         name: shared-network
   ```

3. Update nginx configuration to use the container name:
   ```nginx
   proxy_pass http://your-app-container:3000;
   ```

### Option 2: Host Network

Use `host.docker.internal:3000` (current configuration) if your app runs on the host.

### Option 3: Same Compose File

Add your application services to this docker-compose.yml file.

## Monitoring

### Certificate Expiration

Certificates are automatically renewed, but you can monitor them:

```bash
# Check expiration date
./scripts/manage-ssl.sh status

# Set up a cron job for monitoring
0 0 * * 0 /path/to/ssl-manager/scripts/manage-ssl.sh status | mail -s "SSL Status" <EMAIL>
```

### Health Checks

The nginx service includes health checks. Monitor with:

```bash
docker-compose ps
```

## Security Considerations

1. **Firewall**: Ensure only ports 80 and 443 are exposed
2. **Updates**: Regularly update Docker images
3. **Monitoring**: Set up log monitoring for security events
4. **Backup**: Consider backing up certificates (though they can be regenerated)

## License

This SSL manager is provided as-is for educational and production use.
