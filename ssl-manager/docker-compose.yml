version: '3.8'

services:
  nginx-ssl:
    image: nginx:alpine
    container_name: ssl-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl-params.conf:/etc/nginx/ssl-params.conf:ro
      - letsencrypt_data:/etc/letsencrypt:ro
      - certbot_webroot:/var/www/certbot:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - certbot
    networks:
      - ssl-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  certbot:
    image: certbot/certbot:latest
    container_name: ssl-certbot
    restart: "no"
    volumes:
      - letsencrypt_data:/etc/letsencrypt
      - certbot_webroot:/var/www/certbot
      - ./certbot/logs:/var/log/letsencrypt
    environment:
      - CERTBOT_EMAIL=<EMAIL>
      - CERTBOT_DOMAIN=webmirai.duckdns.org
    command: >
      sh -c "
        trap exit TERM;
        while :; do
          certbot renew --webroot --webroot-path=/var/www/certbot --quiet;
          sleep 12h & wait $${!};
        done;
      "
    networks:
      - ssl-network

  # Certificate initialization service (run once)
  certbot-init:
    image: certbot/certbot:latest
    container_name: ssl-certbot-init
    restart: "no"
    volumes:
      - letsencrypt_data:/etc/letsencrypt
      - certbot_webroot:/var/www/certbot
      - ./certbot/logs:/var/log/letsencrypt
    environment:
      - CERTBOT_EMAIL=<EMAIL>
      - CERTBOT_DOMAIN=webmirai.duckdns.org
    command: >
      certonly --webroot 
      --webroot-path=/var/www/certbot 
      --email <EMAIL> 
      --agree-tos 
      --no-eff-email 
      --force-renewal 
      --non-interactive
      -d webmirai.duckdns.org
    networks:
      - ssl-network
    profiles:
      - init

networks:
  ssl-network:
    driver: bridge
    external: false

volumes:
  letsencrypt_data:
    driver: local
  certbot_webroot:
    driver: local
