version: '3.8'

services:
  nginx:
    image: nginx:latest
    container_name: infinicloud-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - letsencrypt_data:/etc/letsencrypt # Mount the volume for certificates
      - certbot_webroot:/var/www/certbot # Volume for webroot authentication
    depends_on:
      - infinicloud-manager # This assumes infinicloud-manager is in the default docker-compose.yml
    networks:
      - infinicloud-network

  certbot:
    image: certbot/certbot
    container_name: infinicloud-certbot
    volumes:
      - letsencrypt_data:/etc/letsencrypt
      - certbot_conf:/etc/nginx
      - ./nginx/conf.d:/etc/nginx/conf.d:ro # Mount NGINX config for Certbot
      - certbot_webroot:/var/www/certbot # Volume for webroot authentication
    depends_on:
      - nginx
    # Inside your certbot service definition in docker-compose-ssl.yml
    command: >
       certonly --webroot --webroot-path=/var/www/certbot
       --email <EMAIL> --agree-tos --no-eff-email -d webmirai.duckdns.org
       --force-renewal --non-interactive

networks:
  infinicloud-network:
    driver: bridge

volumes:
  certbot_conf:
  letsencrypt_data:
  certbot_webroot:
