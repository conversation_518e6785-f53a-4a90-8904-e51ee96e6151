<template>
  <div class="app-container">
    <div class="main-wrapper">
      <!-- Modern Sidebar -->
      <aside class="modern-sidebar">
        <div class="sidebar-header">
          <h1 class="sidebar-title">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="title-icon">
              <path d="M10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6H12L10,4Z" />
            </svg>
            File Manager
          </h1>
        </div>

        <nav class="sidebar-nav">
          <a href="#" class="nav-item active">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
              <path d="M3,3H21V5H3V3M3,7H21V9H3V7M3,11H21V13H3V11M3,15H21V17H3V15M3,19H21V21H3V19Z" />
            </svg>
            <span>All Files</span>
          </a>
          <a href="#" class="nav-item">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
              <path d="M10,4L12,6H20A2,2 0 0,1 22,8V18A2,2 0 0,1 20,20H4C1.8,20 2,18 2,18V6C2,4.89 2.9,4 4,4H10Z" />
            </svg>
            <span>My Folders</span>
          </a>
          <a href="#" class="nav-item">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5C2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.03L12,21.35Z" />
            </svg>
            <span>Favorites</span>
          </a>
          <a href="/" class="nav-item">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
              <path d="M13,3V9H21V3M13,21H21V11H13M3,21H11V15H3M3,13H11V3H3V13Z" />
            </svg>
            <span>Dashboard</span>
          </a>
        </nav>

        <!-- Storage Usage Widget -->
        <div class="storage-widget" v-if="selectedAccount">
          <h3 class="widget-title">Storage Usage</h3>
          <div class="storage-info">
            <div class="storage-bar">
              <div class="storage-used" :style="{ width: storagePercentage + '%' }"></div>
            </div>
            <div class="storage-text">
              <span class="used">{{ formatBytes(accountStorageUsed) }}</span>
              <span class="total">/ {{ formatBytes(accountStorageTotal) }}</span>
            </div>
          </div>
        </div>
      </aside>

      <!-- Main Content Area -->
      <main class="main-content">
        <!-- Enhanced Header -->
        <header class="content-header">
          <div class="header-top">
            <div class="page-info">
              <h1 class="page-title">All Files</h1>
              <p class="page-subtitle" v-if="selectedAccount">
                {{ selectedAccount.name }} • {{ totalFiles }} files
              </p>
              <p class="page-subtitle" v-else>
                Search across all accounts • {{ totalFiles }} files found
              </p>
            </div>

            <div class="header-buttons">
              <button
                @click="showNewDocumentModal = true"
                class="new-document-button"
                :disabled="!selectedAccount"
                :title="selectedAccount ? 'Create new document in ' + selectedAccount.name : 'Select an account to create documents'"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                </svg>
                <span>New Document</span>
              </button>

              <button
                @click="showUploadModal = true"
                class="upload-button"
                :disabled="!selectedAccount"
                :title="selectedAccount ? 'Upload file to ' + selectedAccount.name : 'Select an account to upload files'"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9,16V10H5L12,3L19,10H15V16H9M5,20V18H19V20H5Z" />
                </svg>
                <span>Upload File</span>
              </button>
            </div>
          </div>

          <!-- Search and Filters -->
          <div class="search-filters-bar">
            <div class="search-container">
              <svg xmlns="http://www.w3.org/2000/svg" class="search-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" />
              </svg>
              <input
                type="text"
                v-model="searchQuery"
                placeholder="Search files and folders..."
                @input="performInstantSearch"
                class="search-input"
              />
              <div class="search-indicators">
                <span v-if="cacheStatus === 'cached'" class="cache-indicator cached" title="Showing cached results">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" />
                  </svg>
                </span>
                <span v-if="cacheStatus === 'live'" class="cache-indicator live" title="Live results">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M13,9V3.5L18.5,9M6,2C4.89,2 4,2.89 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2H6Z" />
                  </svg>
                </span>
              </div>
            </div>

            <div class="filters-container">
              <select v-model="selectedAccount" class="filter-select account-select">
                <option v-if="!accounts.length" :value="null" disabled>No accounts</option>
                <option :value="null">🔍 All Accounts</option>
                <option v-for="account in accounts" :key="account.id" :value="account">
                  {{ account.name }}
                </option>
              </select>

              <select v-model="selectedFileType" class="filter-select type-select">
                <option value="">All Types</option>
                <option value="document">📄 Documents</option>
                <option value="image">🖼️ Images</option>
                <option value="video">🎥 Videos</option>
                <option value="audio">🎵 Audio</option>
                <option value="other">📁 Other</option>
              </select>
            </div>
          </div>
        </header>
        <!-- Files Grid -->
        <div class="files-container">
          <!-- Loading State -->
          <div v-if="loading" class="state-container">
            <div class="loading-spinner">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="spinner">
                <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z" />
              </svg>
            </div>
            <p class="state-text">Loading files...</p>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="state-container error">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="state-icon">
              <path d="M12,2L13.09,8.26L22,9L13.09,9.74L12,16L10.91,9.74L2,9L10.91,8.26L12,2Z" />
            </svg>
            <p class="state-text">{{ error.message }}</p>
          </div>

          <!-- Empty State -->
          <div v-else-if="files.length === 0" class="state-container empty">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="state-icon">
              <path d="M10,4L12,6H20A2,2 0 0,1 22,8V18A2,2 0 0,1 20,20H4C1.8,20 2,18 2,18V6C2,4.89 2.9,4 4,4H10Z" />
            </svg>
            <p class="state-text">No files found</p>
            <p class="state-subtext" v-if="searchQuery">Try adjusting your search or filters</p>
            <p class="state-subtext" v-else-if="!selectedAccount">Select an account to view files</p>
          </div>

          <!-- Files Grid -->
          <div v-else class="files-grid">
            <div
              v-for="file in files"
              :key="file.id || file.path"
              class="file-card"
              @click="previewFile(file)"
            >
              <!-- File Icon/Preview -->
              <div class="file-preview">
                <div class="file-icon" :class="getFileTypeClass(file.type)">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path :d="getFileIconPath(file.type)" />
                  </svg>
                </div>
                <div v-if="!selectedAccount" class="account-badge">
                  {{ file.accountName || 'Unknown' }}
                </div>
              </div>

              <!-- File Info -->
              <div class="file-info">
                <h3 class="file-name" :title="file.name">{{ file.name }}</h3>
                <div class="file-meta">
                  <span class="file-size">{{ formatBytes(file.size) }}</span>
                  <span class="file-date">{{ formatDate(file.lastModified) }}</span>
                </div>
              </div>

              <!-- File Actions -->
              <div class="file-actions" @click.stop>
                <button
                  @click="previewFile(file)"
                  class="action-btn"
                  title="Preview"
                  :disabled="!selectedAccount"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z" />
                  </svg>
                </button>

                <button
                  v-if="isEditableFile(file)"
                  @click="editFile(file)"
                  class="action-btn"
                  title="Edit"
                  :disabled="!selectedAccount"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z" />
                  </svg>
                </button>

                <button
                  @click="downloadFile(file)"
                  class="action-btn"
                  title="Download"
                  :disabled="!selectedAccount"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" />
                  </svg>
                </button>

                <div class="action-dropdown">
                  <button class="action-btn dropdown-toggle" title="More actions">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z" />
                    </svg>
                  </button>
                  <div class="dropdown-menu">
                    <button @click="renameFile(file)" :disabled="!selectedAccount">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z" />
                      </svg>
                      Rename
                    </button>
                    <button @click="deleteFile(file)" class="danger" :disabled="!selectedAccount">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z" />
                      </svg>
                      Delete
                    </button>
                    <button v-if="!selectedAccount && file.accountId" @click="switchToAccount(file.accountId)">
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M13,3V9H21V3M13,21H21V11H13M3,21H11V15H3M3,13H11V3H3V13Z" />
                      </svg>
                      Switch to Account
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Enhanced Pagination -->
        <div class="pagination-section" v-if="files.length > 0">
          <div class="pagination-info">
            <span class="results-count">
              Showing {{ Math.min((currentPage - 1) * itemsPerPage + 1, totalFiles) }}-{{ Math.min(currentPage * itemsPerPage, totalFiles) }} of {{ totalFiles }} files
            </span>
          </div>

          <div class="pagination-controls">
            <button
              @click="prevPage"
              :disabled="currentPage === 1"
              class="pagination-btn nav-btn"
              title="Previous page"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z" />
              </svg>
              <span class="btn-text">Previous</span>
            </button>

            <div class="page-numbers">
              <button
                v-for="page in visiblePages"
                :key="page"
                @click="goToPage(page)"
                :class="['pagination-btn', 'page-btn', {
                  'active': page === currentPage,
                  'ellipsis': page === '...'
                }]"
                :disabled="page === '...'"
              >
                {{ page }}
              </button>
            </div>

            <button
              @click="nextPage"
              :disabled="currentPage >= totalPages"
              class="pagination-btn nav-btn"
              title="Next page"
            >
              <span class="btn-text">Next</span>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
              </svg>
            </button>
          </div>
        </div>
      </main>
    </div>

    <ImageModal
      :show="showImagePreview"
      :image-url="previewImageUrl"
      :image-name="previewImageName"
      @close="closePreviewModal"
    />

    <!-- Enhanced Generic Preview Modal -->
    <div v-if="showGenericPreview" class="modal-overlay" @click.self="closePreviewModal">
        <div class="modal-content enhanced-preview">
            <button @click="closePreviewModal" class="modal-close-btn">×</button>
            <div class="modal-header-section">
                <h2 class="modal-header">{{ previewGenericFile?.name }}</h2>
                <div class="file-info-bar">
                    <span class="file-type-badge">{{ getFileExtension(previewGenericFile?.name || '').toUpperCase() }}</span>
                    <span class="file-size">{{ formatBytes(previewGenericFile?.size || 0) }}</span>
                </div>
            </div>

            <!-- Loading State -->
            <div v-if="previewLoading" class="preview-loading">
                <div class="loading-spinner">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="spinner">
                        <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z" />
                    </svg>
                </div>
                <p>Loading preview...</p>
            </div>

            <!-- Error State -->
            <div v-else-if="previewError" class="preview-error">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="error-icon">
                    <path d="M12,2L13.09,8.26L22,9L13.09,9.74L12,16L10.91,9.74L2,9L10.91,8.26L12,2Z" />
                </svg>
                <p>{{ previewError.message }}</p>
                <button @click="downloadFile(previewGenericFile)" class="download-fallback-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" />
                    </svg>
                    Download File Instead
                </button>
            </div>

            <!-- Preview Content -->
            <div v-else class="modal-body">
                <!-- Video Preview -->
                <video
                    v-if="['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v'].includes(getFileExtension(previewGenericFile?.name || ''))"
                    controls
                    :src="previewGenericContent"
                    class="preview-media"
                >
                    Your browser does not support the video tag.
                </video>

                <!-- Audio Preview -->
                <audio
                    v-else-if="['mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a'].includes(getFileExtension(previewGenericFile?.name || ''))"
                    controls
                    :src="previewGenericContent"
                    class="preview-audio"
                >
                    Your browser does not support the audio tag.
                </audio>

                <!-- PDF Preview -->
                <div v-else-if="getFileExtension(previewGenericFile?.name || '') === 'pdf'" class="pdf-preview-container">
                    <iframe
                        :src="previewGenericContent"
                        class="preview-pdf"
                        title="PDF Preview"
                    ></iframe>
                    <div class="pdf-fallback">
                        <p>If the PDF doesn't load, you can
                            <button @click="downloadFile(previewGenericFile)" class="inline-download-btn">download it here</button>
                        </p>
                    </div>
                </div>

                <!-- Office Documents -->
                <div v-else-if="previewGenericContent === 'office-document'" class="office-preview">
                    <div class="office-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                        </svg>
                    </div>
                    <h3>Office Document</h3>
                    <p>Preview is not available for {{ getFileExtension(previewGenericFile?.name || '').toUpperCase() }} files.</p>
                    <p>Download the file to view it in your preferred application.</p>
                    <button @click="downloadFile(previewGenericFile)" class="download-office-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" />
                        </svg>
                        Download {{ getFileExtension(previewGenericFile?.name || '').toUpperCase() }} File
                    </button>
                </div>

                <!-- CSV Preview -->
                <div v-else-if="getFileExtension(previewGenericFile?.name || '') === 'csv'" class="csv-preview">
                    <div class="csv-table-container">
                        <table class="csv-table">
                            <tbody>
                                <tr v-for="(row, index) in parseCsvContent(previewGenericContent)" :key="index">
                                    <td v-for="(cell, cellIndex) in row" :key="cellIndex">{{ cell }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- JSON Preview -->
                <div v-else-if="getFileExtension(previewGenericFile?.name || '') === 'json'" class="json-preview">
                    <pre class="json-content">{{ formatJsonContent(previewGenericContent) }}</pre>
                </div>

                <!-- Text/Code Preview -->
                <div v-else class="text-preview">
                    <pre class="preview-text" :class="getTextPreviewClass(previewGenericFile?.name || '')">{{ previewGenericContent }}</pre>
                </div>
            </div>
        </div>
    </div>
    <!-- Upload Modal -->
    <div v-if="showUploadModal" class="modal-overlay" @click.self="showUploadModal = false">
      <div class="modal-content">
        <button @click="showUploadModal = false" class="modal-close-btn">×</button>
        <h2 class="modal-header">Upload File</h2>
        <div class="modal-body">
          <FileUploader
            :accountId="selectedAccount?.id"
            @uploaded="handleFileUploaded"
          />
        </div>
      </div>
    </div>

    <!-- New Document Modal -->
    <div v-if="showNewDocumentModal" class="modal-overlay" @click.self="showNewDocumentModal = false">
      <div class="modal-content">
        <button @click="showNewDocumentModal = false" class="modal-close-btn">×</button>
        <h2 class="modal-header">Create New Document</h2>
        <div class="modal-body">
          <div class="document-type-grid">
            <button
              v-for="docType in documentTypes"
              :key="docType.type"
              @click="createNewDocument(docType.type)"
              class="document-type-card"
            >
              <div class="doc-type-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                  <path :d="docType.icon" />
                </svg>
              </div>
              <div class="doc-type-info">
                <h3>{{ docType.name }}</h3>
                <p>{{ docType.description }}</p>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Document Editor -->
    <DocumentEditor
      :show="showDocumentEditor"
      :accountId="editingFile?.accountId || selectedAccount?.id"
      :filePath="editingFile?.path"
      :fileName="editingFile?.name || newDocumentName"
      :fileType="editingFile?.type || newDocumentType"
      :accounts="accounts"
      :defaultAccount="selectedAccount"
      :isNewDocument="isNewDocument"
      @close="closeDocumentEditor"
      @saved="handleDocumentSaved"
    />

    <!-- Notification System -->
    <div class="notification-container">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        :class="['notification', `notification-${notification.type}`]"
        @click="removeNotification(notification.id)"
      >
        <div class="notification-content">
          <svg v-if="notification.type === 'success'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z" />
          </svg>
          <svg v-else-if="notification.type === 'error'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,7A1,1 0 0,0 11,8V12A1,1 0 0,0 12,13A1,1 0 0,0 13,12V8A1,1 0 0,0 12,7M12,17.5A1.5,1.5 0 0,0 13.5,16A1.5,1.5 0 0,0 12,14.5A1.5,1.5 0 0,0 10.5,16A1.5,1.5 0 0,0 12,17.5Z" />
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,7A1,1 0 0,0 11,8V12A1,1 0 0,0 12,13A1,1 0 0,0 13,12V8A1,1 0 0,0 12,7M12,17.5A1.5,1.5 0 0,0 13.5,16A1.5,1.5 0 0,0 12,14.5A1.5,1.5 0 0,0 10.5,16A1.5,1.5 0 0,0 12,17.5Z" />
          </svg>
          <span>{{ notification.message }}</span>
        </div>
        <button @click.stop="removeNotification(notification.id)" class="notification-close">×</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { useDebounceFn } from '@vueuse/core';
import { useRoute } from 'vue-router';
import FileUploader from '@/components/FileUploader.vue';
import DocumentEditor from '@/components/DocumentEditor.vue';

// Get route to access query parameters
const route = useRoute();

// --- State for the main page ---
const files = ref([]);
const loading = ref(true);
const error = ref(null);
const searchQuery = ref('');
const selectedFileType = ref('');
const currentPage = ref(1);
const itemsPerPage = 12; // Increased for grid layout
const totalFiles = ref(0);
const accounts = ref([]);
const selectedAccount = ref(null);
const cacheStatus = ref(''); // 'cached', 'live', or ''

// Storage state for sidebar widget
const accountStorageUsed = ref(0);
const accountStorageTotal = ref(0);
const storagePercentage = computed(() => {
  if (accountStorageTotal.value === 0) return 0;
  return Math.min((accountStorageUsed.value / accountStorageTotal.value) * 100, 100);
});

// Cache state
const filesCache = ref({});
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
const MAX_CACHE_ENTRIES = 20; // Limit cache size

// Global file name cache for all accounts
const globalFileNameCache = ref({});
const GLOBAL_CACHE_KEY = 'global_file_names_cache';
const GLOBAL_CACHE_TTL = 10 * 60 * 1000; // 10 minutes for global cache

// --- State for controlling the modals ---
const showImagePreview = ref(false);
const previewImageUrl = ref('');
const previewImageName = ref('');

// --- State for upload modal ---
const showUploadModal = ref(false);

// --- State for document editor ---
const showNewDocumentModal = ref(false);
const showDocumentEditor = ref(false);
const editingFile = ref(null);
const isNewDocument = ref(false);
const newDocumentName = ref('');
const newDocumentType = ref('docx');

// Document types for new document creation
const documentTypes = ref([
  {
    type: 'docx',
    name: 'Word Document',
    description: 'Rich text document with formatting',
    icon: 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z'
  },
  {
    type: 'txt',
    name: 'Text Document',
    description: 'Plain text document',
    icon: 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z'
  },
  {
    type: 'md',
    name: 'Markdown Document',
    description: 'Markdown formatted text',
    icon: 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z'
  },
  {
    type: 'xlsx',
    name: 'Excel Spreadsheet',
    description: 'Spreadsheet with formulas and charts',
    icon: 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z'
  },
  {
    type: 'csv',
    name: 'CSV File',
    description: 'Comma-separated values',
    icon: 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z'
  },
  {
    type: 'html',
    name: 'HTML Document',
    description: 'Web page document',
    icon: 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z'
  }
]);

// --- State for notifications ---
const notifications = ref([]);

const showGenericPreview = ref(false);
const previewGenericFile = ref(null);
const previewGenericContent = ref('');
const previewLoading = ref(false);
const previewError = ref(null);

// --- Notification System ---
const showNotification = (message, type = 'info') => {
  const id = Date.now();
  notifications.value.push({ id, message, type });

  // Auto-remove after 5 seconds
  setTimeout(() => {
    const index = notifications.value.findIndex(n => n.id === id);
    if (index !== -1) {
      notifications.value.splice(index, 1);
    }
  }, 5000);
};

const removeNotification = (id) => {
  const index = notifications.value.findIndex(n => n.id === id);
  if (index !== -1) {
    notifications.value.splice(index, 1);
  }
};

// --- Simplified Modal Control Functions ---
const closePreviewModal = () => {
  showImagePreview.value = false;
  showGenericPreview.value = false;
  // Clean up blob URL if it exists
  if (previewGenericContent.value.startsWith('blob:')) {
    URL.revokeObjectURL(previewGenericContent.value);
  }
};

// Enhanced file type detection
const getFileExtension = (filename) => {
  return filename.toLowerCase().split('.').pop();
};

const isPreviewableFile = (file) => {
  const ext = getFileExtension(file.name);
  const previewableExtensions = [
    // Images
    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'tiff', 'ico',
    // Videos
    'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v',
    // Audio
    'mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a',
    // Documents
    'pdf', 'txt', 'md', 'json', 'xml', 'csv', 'html', 'htm',
    // Office documents (limited preview)
    'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'
  ];
  return previewableExtensions.includes(ext);
};

const previewFile = async (file) => {
  if (!selectedAccount.value) {
    showNotification('Please select an account to preview files', 'error');
    return;
  }

  if (!isPreviewableFile(file)) {
    showNotification('Preview not available for this file type. You can download it instead.', 'info');
    return;
  }

  const ext = getFileExtension(file.name);

  // Handle images with the specialized image modal
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'tiff', 'ico'].includes(ext)) {
    const accountId = selectedAccount.value.id;
    const encodedFilePath = encodeURIComponent(file.path);
    previewImageUrl.value = `/api/file-content/${accountId}/${encodedFilePath}`;
    previewImageName.value = file.name;
    showImagePreview.value = true;
    return;
  }

  // Handle all other previewable files with the generic modal
  showGenericPreview.value = true;
  previewGenericFile.value = file;
  previewLoading.value = true;
  previewError.value = null;

  try {
    const accountId = selectedAccount.value.id;
    const encodedFilePath = encodeURIComponent(file.path);
    const contentUrl = `/api/file-content/${accountId}/${encodedFilePath}`;
    const response = await fetch(contentUrl);

    if (!response.ok) {
      throw new Error(`Failed to load file: ${response.status} ${response.statusText}`);
    }

    // Handle different file types
    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v'].includes(ext)) {
      // Video files
      const blob = await response.blob();
      previewGenericContent.value = URL.createObjectURL(blob);
    } else if (['mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a'].includes(ext)) {
      // Audio files
      const blob = await response.blob();
      previewGenericContent.value = URL.createObjectURL(blob);
    } else if (ext === 'pdf') {
      // PDF files
      const blob = await response.blob();
      previewGenericContent.value = URL.createObjectURL(blob);
    } else if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(ext)) {
      // Office documents - show download option
      previewGenericContent.value = 'office-document';
    } else {
      // Text-based files (txt, md, json, xml, csv, html, etc.)
      const text = await response.text();
      previewGenericContent.value = text;
    }
  } catch (e) {
    console.error('Preview error:', e);
    previewError.value = e;
  } finally {
    previewLoading.value = false;
  }
};

// --- All other existing logic (fetchFiles, deleteFile, etc.) remains the same ---
const totalPages = computed(() => {
    if (totalFiles.value === 0) return 1;
    return Math.ceil(totalFiles.value / itemsPerPage);
});

// Computed property for visible page numbers with ellipsis
const visiblePages = computed(() => {
  const total = totalPages.value;
  const current = currentPage.value;
  const pages = [];

  if (total <= 7) {
    // Show all pages if total is 7 or less
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    // Always show first page
    pages.push(1);

    if (current <= 4) {
      // Show pages 2, 3, 4, 5, ..., last
      for (let i = 2; i <= 5; i++) {
        pages.push(i);
      }
      pages.push('...');
      pages.push(total);
    } else if (current >= total - 3) {
      // Show 1, ..., last-4, last-3, last-2, last-1, last
      pages.push('...');
      for (let i = total - 4; i <= total; i++) {
        pages.push(i);
      }
    } else {
      // Show 1, ..., current-1, current, current+1, ..., last
      pages.push('...');
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i);
      }
      pages.push('...');
      pages.push(total);
    }
  }

  return pages;
});
const fetchAccounts = async () => {
  try {
    const response = await fetch('/api/accounts');
    if (!response.ok) throw new Error('Failed to fetch accounts');
    const data = await response.json();
    accounts.value = data.accounts || [];

    // Check if there's an accountId in the URL query parameters
    const accountIdFromUrl = route.query.accountId;

    if (accountIdFromUrl && accounts.value.length > 0) {
      // Find the account that matches the URL parameter
      const targetAccount = accounts.value.find(account => account.id === accountIdFromUrl);
      if (targetAccount) {
        selectedAccount.value = targetAccount;
        console.log('Selected account from URL:', targetAccount.name);
      } else {
        // If the account ID from URL is not found, default to first account
        selectedAccount.value = accounts.value[0];
        console.warn('Account ID from URL not found, defaulting to first account');
      }
    } else if (accounts.value.length > 0) {
      // No URL parameter, default to first account
      selectedAccount.value = accounts.value[0];
    }
  } catch (e) {
    console.error('Error fetching accounts:', e);
    error.value = e;
  }
};
const fetchFiles = async () => {
  // Handle "Search All Accounts" mode
  if (!selectedAccount.value) {
    if (searchQuery.value.trim()) {
      // Search across all accounts using global cache
      const allResults = searchAllAccounts(searchQuery.value);

      // Apply file type filter if selected
      const typeFilteredResults = selectedFileType.value
        ? allResults.filter(file => file.type === selectedFileType.value)
        : allResults;

      // Apply pagination
      const startIndex = (currentPage.value - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      files.value = typeFilteredResults.slice(startIndex, endIndex);
      totalFiles.value = typeFilteredResults.length;
      loading.value = false;
      cacheStatus.value = 'cached';
      console.log(`Cross-account search found ${typeFilteredResults.length} results`);
      return;
    } else {
      // No search query and no account selected
      files.value = [];
      totalFiles.value = 0;
      loading.value = false;
      error.value = new Error('Please select an account or enter a search query to search all accounts.');
      return;
    }
  }

  // Regular single-account mode
  const cacheKey = `${selectedAccount.value.id}:${searchQuery.value}:${selectedFileType.value}:${currentPage.value}`;
  const cacheEntry = filesCache.value[cacheKey];
  if (cacheEntry && (Date.now() - cacheEntry.timestamp < CACHE_TTL)) {
    files.value = cacheEntry.data.files;
    totalFiles.value = cacheEntry.data.totalFiles;
    loading.value = false;
    const wasRefreshing = cacheEntry.refreshing;
    if (!wasRefreshing) {
      cacheEntry.refreshing = true;
      refreshFilesInBackground(cacheKey);
    }
    return;
  }
  const sessionData = sessionStorage.getItem(cacheKey);
  if (sessionData) {
    const parsedData = JSON.parse(sessionData);
    files.value = parsedData.files;
    totalFiles.value = parsedData.totalFiles;
    loading.value = false;
  } else {
    loading.value = true;
  }
  error.value = null;
  files.value = files.value || [];
  const accountCacheKeys = Object.keys(filesCache.value).filter(key =>
    key.startsWith(`${selectedAccount.value.id}:`)
  );
  if (accountCacheKeys.length > 0 && searchQuery.value) {
    const allCachedFiles = [];
    accountCacheKeys.forEach(key => {
      allCachedFiles.push(...filesCache.value[key].data.files);
    });
    const query = searchQuery.value.toLowerCase();
    const filteredFiles = allCachedFiles.filter(file =>
      file.name.toLowerCase().includes(query)
    );
    if (filteredFiles.length > 0) {
      files.value = filteredFiles.slice(0, itemsPerPage);
      totalFiles.value = filteredFiles.length;
      loading.value = false;
      cacheStatus.value = 'cached';
    }
  }
  try {
    const accountId = selectedAccount.value.id;
    const url = `/api/search-files/${accountId}?query=${searchQuery.value}&type=${selectedFileType.value}&page=${currentPage.value}&limit=${itemsPerPage}`;
    const response = await fetch(url);
    if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || 'Failed to fetch files');
    }
    const data = await response.json();
    if (data && data.files) {
      // Debug: Log the first few files to verify account-specific content
      console.log(`[DEBUG] Account ${accountId} returned ${data.files.length} files. First 3:`,
        data.files.slice(0, 3).map(f => ({ name: f.name, path: f.path })));

      if (!files.value.length || JSON.stringify(files.value) !== JSON.stringify(data.files)) {
        files.value = data.files;
        totalFiles.value = data.totalFiles || 0;
        cacheStatus.value = 'live';
      }
      filesCache.value[cacheKey] = {
        data: { files: [...data.files], totalFiles: data.totalFiles || 0 },
        timestamp: Date.now(),
        refreshing: false
      };
      sessionStorage.setItem(cacheKey, JSON.stringify({
        files: data.files,
        totalFiles: data.totalFiles || 0
      }));
      const keys = Object.keys(filesCache.value);
      if (keys.length > MAX_CACHE_ENTRIES) {
        const oldestKey = keys.reduce((oldest, key) =>
          filesCache.value[key].timestamp < filesCache.value[oldest].timestamp ? key : oldest
        );
        delete filesCache.value[oldestKey];
      }
    }
  } catch (e) {
    console.error('Server search failed', e);
    if (!files.value.length) {
      error.value = e;
      files.value = [];
      totalFiles.value = 0;
    }
  } finally {
    loading.value = false;
  }
};
const refreshFilesInBackground = async (cacheKey) => {
  if (!selectedAccount.value) return;
  try {
    const accountId = selectedAccount.value.id;
    const url = `/api/search-files/${accountId}?query=${searchQuery.value}&type=${selectedFileType.value}&page=${currentPage.value}&limit=${itemsPerPage}`;
    const response = await fetch(url);
    if (!response.ok) return;
    const data = await response.json();
    if (data && data.files) {
      filesCache.value[cacheKey] = {
        data: { files: data.files, totalFiles: data.totalFiles || 0 },
        timestamp: Date.now(),
        refreshing: false
      };
      if (cacheKey === `${selectedAccount.value.id}:${searchQuery.value}:${selectedFileType.value}:${currentPage.value}`) {
        files.value = data.files;
        totalFiles.value = data.totalFiles || 0;
        cacheStatus.value = 'live';
      }
    }
  } catch (e) {
    console.error('Background refresh failed', e);
  } finally {
    if (filesCache.value[cacheKey]) {
      filesCache.value[cacheKey].refreshing = false;
    }
  }
};
// Enhanced instant search with global cache support
const performInstantSearch = () => {
  if (!searchQuery.value.trim()) {
    fetchFiles();
    return;
  }

  const query = searchQuery.value.toLowerCase();
  let foundCachedResults = false;

  // First, try to search in current account's cache
  const accountCacheKeys = Object.keys(filesCache.value).filter(key =>
    key.startsWith(`${selectedAccount.value?.id}:`)
  );

  if (accountCacheKeys.length > 0) {
    const allCachedFiles = [];
    accountCacheKeys.forEach(key => {
      if (filesCache.value[key]?.data?.files) {
        allCachedFiles.push(...filesCache.value[key].data.files);
      }
    });

    const filteredFiles = allCachedFiles.filter(file =>
      file.name.toLowerCase().includes(query) ||
      file.path.toLowerCase().includes(query)
    );

    if (filteredFiles.length > 0) {
      // Apply file type filter if selected
      const typeFilteredFiles = selectedFileType.value
        ? filteredFiles.filter(file => file.type === selectedFileType.value)
        : filteredFiles;

      // Update UI immediately with cached results
      files.value = typeFilteredFiles.slice(0, itemsPerPage);
      totalFiles.value = typeFilteredFiles.length;
      cacheStatus.value = 'cached';
      loading.value = false;
      foundCachedResults = true;
    }
  }

  // If no results in current account cache, try global cache
  if (!foundCachedResults && selectedAccount.value && globalFileNameCache.value[selectedAccount.value.id]) {
    const globalFiles = globalFileNameCache.value[selectedAccount.value.id].files;
    const filteredFiles = globalFiles.filter(file =>
      file.name.toLowerCase().includes(query) ||
      file.path.toLowerCase().includes(query)
    );

    if (filteredFiles.length > 0) {
      // Apply file type filter if selected
      const typeFilteredFiles = selectedFileType.value
        ? filteredFiles.filter(file => file.type === selectedFileType.value)
        : filteredFiles;

      // Update UI with global cache results
      files.value = typeFilteredFiles.slice(0, itemsPerPage);
      totalFiles.value = typeFilteredFiles.length;
      cacheStatus.value = 'cached';
      loading.value = false;
      console.log(`Found ${typeFilteredFiles.length} results in global cache for account ${selectedAccount.value.name}`);
    }
  }

  // Always perform server search in background for fresh results
  debouncedServerSearch();
};

const debouncedServerSearch = useDebounceFn(() => {
  currentPage.value = 1;
  fetchFiles();
}, 300);

// Global file name cache functions
const loadGlobalFileNameCache = () => {
  try {
    const cached = localStorage.getItem(GLOBAL_CACHE_KEY);
    if (cached) {
      const parsedCache = JSON.parse(cached);
      if (Date.now() - parsedCache.timestamp < GLOBAL_CACHE_TTL) {
        globalFileNameCache.value = parsedCache.data;
        console.log('Loaded global file name cache with', Object.keys(globalFileNameCache.value).length, 'accounts');
        return true;
      }
    }
  } catch (e) {
    console.error('Error loading global file name cache:', e);
  }
  return false;
};

const saveGlobalFileNameCache = () => {
  try {
    const cacheData = {
      data: globalFileNameCache.value,
      timestamp: Date.now()
    };
    localStorage.setItem(GLOBAL_CACHE_KEY, JSON.stringify(cacheData));
    console.log('Saved global file name cache');
  } catch (e) {
    console.error('Error saving global file name cache:', e);
  }
};

const updateGlobalFileNameCache = async () => {
  if (!accounts.value.length) return;

  console.log('Updating global file name cache for all accounts...');

  for (const account of accounts.value) {
    try {
      const response = await fetch(`/api/search-files/${account.id}?query=&type=&page=1&limit=1000`);
      if (response.ok) {
        const data = await response.json();
        if (data && data.files) {
          globalFileNameCache.value[account.id] = {
            accountName: account.name,
            files: data.files.map(f => ({
              name: f.name,
              path: f.path,
              type: f.type,
              size: f.size
            })),
            lastUpdated: Date.now()
          };
        }
      }
    } catch (e) {
      console.error(`Error updating cache for account ${account.name}:`, e);
    }
  }

  saveGlobalFileNameCache();
  console.log('Global file name cache updated for', Object.keys(globalFileNameCache.value).length, 'accounts');
};

// Cross-account search function
const searchAllAccounts = (query) => {
  if (!query.trim() || Object.keys(globalFileNameCache.value).length === 0) {
    return [];
  }

  const searchTerm = query.toLowerCase();
  const allResults = [];

  Object.entries(globalFileNameCache.value).forEach(([accountId, accountData]) => {
    const matchingFiles = accountData.files.filter(file =>
      file.name.toLowerCase().includes(searchTerm) ||
      file.path.toLowerCase().includes(searchTerm)
    );

    matchingFiles.forEach(file => {
      allResults.push({
        ...file,
        accountId,
        accountName: accountData.accountName,
        id: `${accountId}:${file.path}` // Unique ID for cross-account results
      });
    });
  });

  return allResults;
};

// Switch to a specific account
const switchToAccount = (accountId) => {
  const targetAccount = accounts.value.find(account => account.id === accountId);
  if (targetAccount) {
    selectedAccount.value = targetAccount;
    searchQuery.value = ''; // Clear search when switching accounts
    currentPage.value = 1;
    fetchFiles();
    showNotification(`Switched to account: ${targetAccount.name}`, 'success');
  }
};
onMounted(async () => {
  // Load global file name cache first
  const cacheLoaded = loadGlobalFileNameCache();

  await fetchAccounts();
  if (selectedAccount.value) {
    fetchFiles();
  } else {
    loading.value = false;
  }

  // Update global cache in background if not loaded or if it's been a while
  if (!cacheLoaded || Object.keys(globalFileNameCache.value).length === 0) {
    setTimeout(() => {
      updateGlobalFileNameCache();
    }, 2000); // Delay to not interfere with initial loading
  }
});
watch([selectedFileType, selectedAccount], () => {
  currentPage.value = 1;
  fetchFiles();
});

// Watch for route changes to handle direct URL navigation
watch(() => route.query.accountId, async (newAccountId) => {
  if (newAccountId && accounts.value.length > 0) {
    const targetAccount = accounts.value.find(account => account.id === newAccountId);
    if (targetAccount && targetAccount.id !== selectedAccount.value?.id) {
      selectedAccount.value = targetAccount;
      console.log('Account changed via URL:', targetAccount.name);

      // Clear all caches when switching accounts to ensure fresh data
      filesCache.value = {};
      sessionStorage.clear();
    }
  }
});
const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    fetchFiles();
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    fetchFiles();
  }
};

const goToPage = (page) => {
  if (page !== '...' && page >= 1 && page <= totalPages.value && page !== currentPage.value) {
    currentPage.value = page;
    fetchFiles();
  }
};
const renameFile = async (file) => {
  const newName = prompt(`Enter new name for "${file.name}"`, file.name);
  if (!newName || newName === file.name) return;

  const accountId = selectedAccount.value.id;
  const oldPath = file.path;
  const lastSlash = oldPath.lastIndexOf('/');
  const newPath = lastSlash === -1 ? newName : oldPath.substring(0, lastSlash + 1) + newName;

  // Immediately update UI for instant feedback
  const fileIndex = files.value.findIndex(f => f.id === file.id);
  const originalName = file.name;
  const originalPath = file.path;

  if (fileIndex !== -1) {
    files.value[fileIndex].name = newName;
    files.value[fileIndex].path = newPath;
  }

  try {
    // Encode the old path for the URL, but the API will decode it
    const encodedOldPath = encodeURIComponent(oldPath);
    const response = await fetch(`/api/rename-file/${accountId}/${encodedOldPath}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ newPath })
    });

    const responseData = await response.json();

    if (!response.ok) {
      throw new Error(responseData.error || `HTTP error! status: ${response.status}`);
    }

    // Check if the response indicates success
    if (responseData.success) {
      // Clear cache for this account
      Object.keys(filesCache.value).forEach(key => {
        if (key.startsWith(`${accountId}:`)) {
          delete filesCache.value[key];
        }
      });

      // Show success notification
      showNotification(`File renamed to "${newName}" successfully!`, 'success');
    } else {
      throw new Error(responseData.error || 'Unknown error occurred');
    }
  } catch (e) {
    console.error('Rename file error:', e);

    // Revert UI changes on error
    if (fileIndex !== -1) {
      files.value[fileIndex].name = originalName;
      files.value[fileIndex].path = originalPath;
    }

    showNotification(`Failed to rename file: ${e.message}`, 'error');
  }
};

const deleteFile = async (file) => {
  if (!confirm(`Are you sure you want to delete "${file.name}"?`)) return;

  const accountId = selectedAccount.value.id;

  // Immediately remove from UI for instant feedback
  const fileIndex = files.value.findIndex(f => f.id === file.id);
  const removedFile = files.value[fileIndex];

  if (fileIndex !== -1) {
    files.value.splice(fileIndex, 1);
    totalFiles.value = Math.max(0, totalFiles.value - 1);
  }

  try {
    const encodedFilePath = encodeURIComponent(file.path);
    const response = await fetch(`/api/delete-file/${accountId}/${encodedFilePath}`, { method: 'DELETE' });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    // Clear cache for this account
    Object.keys(filesCache.value).forEach(key => {
      if (key.startsWith(`${accountId}:`)) {
        delete filesCache.value[key];
      }
    });

    showNotification(`File "${file.name}" deleted successfully.`, 'success');
  } catch (e) {
    console.error('Delete file error:', e);

    // Revert UI changes on error
    if (fileIndex !== -1 && removedFile) {
      files.value.splice(fileIndex, 0, removedFile);
      totalFiles.value += 1;
    }

    showNotification(`Failed to delete file: ${e.message}`, 'error');
  }
};
const downloadFile = async (file) => {
  try {
    const accountId = selectedAccount.value.id;
    const encodedFilePath = encodeURIComponent(file.path);
    const response = await fetch(`/api/file-content/${accountId}/${encodedFilePath}`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  } catch (e) {
    alert(`Failed to download file: ${e.message}`);
  }
};
const formatBytes = (bytes, decimals = 2) => {
  if (!+bytes) return '0 Bytes';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
};
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric', month: 'short', day: 'numeric'
  });
};

// --- File Type Helpers ---
const getFileTypeClass = (type) => {
  const typeMap = {
    'document': 'file-document',
    'image': 'file-image',
    'video': 'file-video',
    'audio': 'file-audio',
    'directory': 'file-folder',
    'other': 'file-other'
  };
  return typeMap[type] || 'file-other';
};

const getFileIconPath = (type) => {
  const iconMap = {
    'document': 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z',
    'image': 'M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z',
    'video': 'M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z',
    'audio': 'M12,3V13.55C11.41,13.21 10.73,13 10,13A3,3 0 0,0 7,16A3,3 0 0,0 10,19A3,3 0 0,0 13,16V7H18V5H12Z',
    'directory': 'M10,4L12,6H20A2,2 0 0,1 22,8V18A2,2 0 0,1 20,20H4C1.8,20 2,18 2,18V6C2,4.89 2.9,4 4,4H10Z',
    'other': 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z'
  };
  return iconMap[type] || iconMap['other'];
};

// --- Preview Helper Functions ---
const parseCsvContent = (csvText) => {
  if (!csvText) return [];

  const lines = csvText.split('\n').filter(line => line.trim());
  const maxRows = 50; // Limit rows for performance
  const limitedLines = lines.slice(0, maxRows);

  return limitedLines.map(line => {
    // Simple CSV parsing - handles basic cases
    const cells = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        cells.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    cells.push(current.trim());

    return cells;
  });
};

const formatJsonContent = (jsonText) => {
  try {
    const parsed = JSON.parse(jsonText);
    return JSON.stringify(parsed, null, 2);
  } catch (e) {
    return jsonText; // Return original if parsing fails
  }
};

const getTextPreviewClass = (filename) => {
  const ext = getFileExtension(filename);
  const codeExtensions = ['js', 'ts', 'html', 'css', 'xml', 'md', 'py', 'java', 'cpp', 'c', 'php'];
  return codeExtensions.includes(ext) ? 'code-preview' : 'text-preview';
};
const handleFileUploaded = async () => {
  showUploadModal.value = false;

  // Clear cache for this account to ensure fresh data
  if (selectedAccount.value) {
    Object.keys(filesCache.value).forEach(key => {
      if (key.startsWith(`${selectedAccount.value.id}:`)) {
        delete filesCache.value[key];
      }
    });

    // Clear session storage for this account
    const sessionKeys = Object.keys(sessionStorage);
    sessionKeys.forEach(key => {
      if (key.includes(selectedAccount.value.id)) {
        sessionStorage.removeItem(key);
      }
    });
  }

  // Show immediate feedback
  showNotification('File uploaded successfully! Refreshing file list...', 'success');

  // Immediately refresh the file list to show the new file
  await fetchFiles();

  // Update the global file name cache
  await updateGlobalFileNameCache();
};

// Document editing functions
const isEditableFile = (file) => {
  const ext = getFileExtension(file.name).toLowerCase();
  const editableExtensions = ['docx', 'txt', 'md', 'html', 'xlsx', 'xls', 'csv'];
  return editableExtensions.includes(ext);
};

const editFile = (file) => {
  if (!selectedAccount.value) {
    showNotification('Please select an account to edit files', 'error');
    return;
  }

  editingFile.value = {
    ...file,
    accountId: selectedAccount.value.id
  };
  isNewDocument.value = false;
  showDocumentEditor.value = true;
};

const createNewDocument = (docType) => {
  if (!selectedAccount.value) {
    showNotification('Please select an account to create documents', 'error');
    return;
  }

  editingFile.value = null;
  isNewDocument.value = true;
  newDocumentName.value = `Untitled.${docType}`;
  newDocumentType.value = docType;
  showNewDocumentModal.value = false;
  showDocumentEditor.value = true;
};

const closeDocumentEditor = () => {
  showDocumentEditor.value = false;
  editingFile.value = null;
  isNewDocument.value = false;
  newDocumentName.value = '';
  newDocumentType.value = 'docx';
};

const handleDocumentSaved = async (saveData) => {
  showNotification('Document saved successfully!', 'success');

  // Clear cache for this account to ensure fresh data
  if (saveData.account) {
    Object.keys(filesCache.value).forEach(key => {
      if (key.startsWith(`${saveData.account.id}:`)) {
        delete filesCache.value[key];
      }
    });
  }

  // Refresh the file list
  await fetchFiles();

  // Close the editor if it was a new document
  if (isNewDocument.value) {
    closeDocumentEditor();
  }
};
</script>

<style scoped>
/* Modern File Manager Styles */

/* CSS Variables */
:root {
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --primary-light: #e0e7ff;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --bg-color: #f8fafc;
  --sidebar-bg: #ffffff;
  --card-bg: #ffffff;
  --text-color: #1e293b;
  --text-muted: #0069fd;
  --text-light: #94a3b8;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --border-radius: 0.75rem;
  --border-radius-sm: 0.5rem;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Layout */
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-color) 0%, #f1f5f9 100%);
  font-family: var(--font-family);
  color: var(--text-color);
}

.main-wrapper {
  display: flex;
  min-height: 100vh;
}

/* Modern Sidebar */
.modern-sidebar {
  width: 280px;
  background: var(--sidebar-bg);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  position: relative;
  box-shadow: var(--shadow-sm);
}

.sidebar-header {
  padding: 2rem 1.5rem 1.5rem;
  border-bottom: 1px solid var(--border-light);
}

.sidebar-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
}

.title-icon {
  width: 28px;
  height: 28px;
  color: var(--primary-color);
}

.main-content {
  flex: 1;
  background: transparent;
  overflow: hidden;
}

/* Sidebar Navigation */
.sidebar-nav {
  padding: 1rem 1.5rem;
  flex: 1;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.875rem;
  padding: 0.875rem 1rem;
  margin-bottom: 0.5rem;
  border-radius: var(--border-radius-sm);
  text-decoration: none;
  color: var(--text-muted);
  font-weight: 500;
  font-size: 0.95rem;
  transition: var(--transition);
  position: relative;
}

.nav-item:hover {
  background: var(--primary-light);
  color: var(--primary-color);
  transform: translateX(2px);
}

.nav-item.active {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-md);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: -1.5rem;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: var(--primary-color);
  border-radius: 0 2px 2px 0;
}

.nav-item svg {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* Storage Widget */
.storage-widget {
  margin: 1.5rem;
  padding: 1.25rem;
  background: linear-gradient(135deg, var(--primary-light) 0%, #f0f9ff 100%);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.widget-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 1rem 0;
}

.storage-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.storage-bar {
  height: 8px;
  background: var(--border-light);
  border-radius: 4px;
  overflow: hidden;
}

.storage-used {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  border-radius: 4px;
  transition: var(--transition);
}

.storage-text {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
}

.storage-text .used {
  color: var(--primary-color);
  font-weight: 600;
}

.storage-text .total {
  color: var(--text-muted);
}

/* Content Header */
.content-header {
  padding: 2rem 2.5rem 1.5rem;
  background: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.page-info {
  flex: 1;
}

.page-title {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.025em;
}

.page-subtitle {
  color: var(--text-muted);
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

.header-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.new-document-button,
.upload-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1.5rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.new-document-button {
  background: #10b981;
}

.new-document-button:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.upload-button:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.new-document-button:disabled,
.upload-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.new-document-button svg,
.upload-button svg {
  width: 20px;
  height: 20px;
}

/* Search and Filters Bar */
.search-filters-bar {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-container {
  position: relative;
  flex: 1;
  min-width: 320px;
}

.search-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 1rem;
  background: var(--card-bg);
  color: var(--text-color);
  transition: var(--transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-input::placeholder {
  color: var(--text-light);
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--text-muted);
  pointer-events: none;
}

.search-indicators {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 0.5rem;
}

.cache-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  transition: var(--transition);
}

.cache-indicator.cached {
  background: var(--warning-color);
  color: white;
}

.cache-indicator.live {
  background: var(--success-color);
  color: white;
}

.cache-indicator svg {
  width: 12px;
  height: 12px;
}

.filters-container {
  display: flex;
  gap: 1rem;
  align-items: center;
  position: relative;
  z-index: 60;
}

.filter-select {
  padding: 0.875rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background: var(--card-bg);
  color: var(--text-color);
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  min-width: 140px;
  position: relative;
  z-index: 50;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.filter-select:hover {
  border-color: var(--primary-color);
}

/* Files Container */
.files-container {
  padding: 2rem 2.5rem;
  min-height: 60vh;
}

/* State Containers */
.state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  margin-bottom: 1.5rem;
}

.spinner {
  width: 48px;
  height: 48px;
  color: var(--primary-color);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.state-icon {
  width: 64px;
  height: 64px;
  color: var(--text-light);
  margin-bottom: 1.5rem;
}

.state-container.error .state-icon {
  color: var(--danger-color);
}

.state-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.state-container.error .state-text {
  color: var(--danger-color);
}

.state-subtext {
  color: var(--text-muted);
  font-size: 1rem;
  margin: 0;
}

/* Files Grid */
.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 1rem 0;
}

/* File Cards */
.file-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: visible;
  z-index: 1;
}

.file-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  z-index: 10;
}

.file-card:hover .file-actions {
  opacity: 1;
  transform: translateY(-2px);
}

/* File Preview */
.file-preview {
  position: relative;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px;
}

.file-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-sm);
  transition: var(--transition);
}

.file-icon svg {
  width: 32px;
  height: 32px;
}

/* File Type Colors */
.file-document {
  background: #dbeafe;
  color: #2563eb;
}

.file-image {
  background: #dcfce7;
  color: #16a34a;
}

.file-video {
  background: #fef3c7;
  color: #d97706;
}

.file-audio {
  background: #fce7f3;
  color: #db2777;
}

.file-folder {
  background: #f3e8ff;
  color: #9333ea;
}

.file-other {
  background: #f1f5f9;
  color: var(--text-muted);
}

.account-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: var(--primary-color);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 0 var(--border-radius-sm) 0 var(--border-radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* File Info */
.file-info {
  margin-bottom: 1rem;
}

.file-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.file-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: var(--text-muted);
}

.file-size {
  font-weight: 500;
}

.file-date {
  font-weight: 400;
}

/* File Actions */
.file-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  opacity: 0.7;
  transform: translateY(0);
  transition: var(--transition);
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background: var(--card-bg);
  color: var(--text-color);
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.action-btn:hover:not(:disabled) {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  transform: scale(1.05);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--border-light);
  color: var(--text-light);
}

.action-btn svg {
  width: 18px;
  height: 18px;
}

/* Action Dropdown */
.action-dropdown {
  position: relative;
  z-index: 100;
}

.dropdown-toggle {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-lg);
  padding: 0.5rem 0;
  min-width: 160px;
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: var(--transition);
  z-index: 200;
}

.action-dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-menu button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  color: var(--text-color);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-align: left;
}

.dropdown-menu button:hover:not(:disabled) {
  background: var(--primary-light);
  color: var(--primary-color);
}

.dropdown-menu button.danger:hover:not(:disabled) {
  background: #fef2f2;
  color: var(--danger-color);
}

.dropdown-menu button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dropdown-menu button svg {
  width: 16px;
  height: 16px;
}

/* Pagination Section */
.pagination-section {
  padding: 1.5rem 2.5rem 2rem;
  background: var(--card-bg);
  border-top: 1px solid var(--border-color);
}

.pagination-info {
  text-align: center;
  margin-bottom: 1.5rem;
}

.results-count {
  color: var(--text-muted);
  font-size: 0.95rem;
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  height: 44px;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background: var(--card-bg);
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.95rem;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
}

.pagination-btn:hover:not(:disabled):not(.ellipsis) {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--border-light);
  color: var(--text-light);
}

.pagination-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  font-weight: 600;
  box-shadow: var(--shadow-md);
}

.pagination-btn.ellipsis {
  border: none;
  background: none;
  cursor: default;
  color: var(--text-muted);
  font-weight: normal;
}

.pagination-btn.ellipsis:hover {
  background: none;
  transform: none;
  box-shadow: none;
}

.nav-btn {
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  min-width: auto;
}

.nav-btn svg {
  width: 16px;
  height: 16px;
}

.btn-text {
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .files-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {
  .modern-sidebar {
    width: 260px;
  }

  .content-header {
    padding: 1.5rem 1.5rem 1rem;
  }

  .header-top {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
  }

  .search-filters-bar {
    flex-direction: column;
    gap: 1rem;
  }

  .search-container {
    min-width: auto;
  }

  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-select {
    min-width: auto;
  }

  .files-container {
    padding: 1.5rem;
  }

  .files-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1rem;
  }

  .pagination-section {
    padding: 1rem 1.5rem;
  }

  .pagination-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .page-numbers {
    order: 1;
  }

  .nav-btn {
    order: 2;
  }

  .pagination-btn {
    min-width: 40px;
    height: 40px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .modern-sidebar {
    width: 240px;
  }

  .page-title {
    font-size: 1.875rem;
  }

  .files-grid {
    grid-template-columns: 1fr;
  }

  .file-card {
    padding: 1.25rem;
  }
}

/* 10. State & Modal Styles */
.state-cell {
  text-align: center;
  padding: 3rem;
  color: var(--text-muted);
}
.state-cell.error {
  color: var(--danger-color);
}
.modal-overlay {
    position: fixed; inset: 0; background-color: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(4px);
    display: flex; align-items: center; justify-content: center; z-index: 1000;
    animation: fadeIn 0.3s ease;
}
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
.modal-content {
    background-color: white; padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: 0 0.5rem 1.5rem rgba(0,0,0,0.15);
    max-width: 80vw; max-height: 90vh; overflow-y: auto; position: relative;
    width: 700px;
}

.enhanced-preview {
    max-width: 90vw;
    max-height: 90vh;
    width: 900px;
}

.modal-header-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.file-info-bar {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 0.5rem;
}

.file-type-badge {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.file-size {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.preview-loading, .preview-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
}

.preview-loading .spinner {
    width: 48px;
    height: 48px;
    color: var(--primary-color);
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.error-icon {
    width: 48px;
    height: 48px;
    color: var(--danger-color);
    margin-bottom: 1rem;
}

.download-fallback-btn, .download-office-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    margin-top: 1rem;
}

.download-fallback-btn:hover, .download-office-btn:hover {
    background: var(--primary-hover);
}

.inline-download-btn {
    color: var(--primary-color);
    text-decoration: underline;
    background: none;
    border: none;
    cursor: pointer;
    font-size: inherit;
}

.preview-audio {
    width: 100%;
    max-width: 500px;
    margin: 2rem auto;
    display: block;
}

.pdf-preview-container {
    position: relative;
}

.preview-pdf {
    width: 100%;
    height: 70vh;
    border: none;
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
}

.pdf-fallback {
    margin-top: 1rem;
    text-align: center;
    color: var(--text-muted);
    font-size: 0.875rem;
}

.office-preview {
    text-align: center;
    padding: 3rem;
}

.office-icon {
    margin-bottom: 1.5rem;
}

.office-icon svg {
    width: 64px;
    height: 64px;
    color: var(--text-muted);
}

.office-preview h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 1rem 0;
}

.office-preview p {
    color: var(--text-muted);
    margin: 0.5rem 0;
}
.modal-close-btn {
    position: absolute; top: 1rem; right: 1rem; background: none; border: none;
    font-size: 2rem; line-height: 1; cursor: pointer; color: var(--text-muted);
    transition: color 0.2s;
}
.modal-close-btn:hover {
    color: var(--text-color);
}
.modal-header {
    font-size: 1.5rem; font-weight: 600; margin: 0; margin-bottom: 1.5rem;
}
.modal-body { margin-top: 1rem; }
.preview-media {
    max-width: 100%; max-height: 70vh; display: block; margin: 0 auto;
    border-radius: var(--border-radius);
}
.preview-text {
    background-color: #f1f3f5; padding: 1rem; border: 1px solid var(--border-color);
    white-space: pre-wrap; word-break: break-all; max-height: 60vh; overflow-y: auto;
    border-radius: var(--border-radius);
    font-family: 'Courier New', Courier, monospace;
}

/* CSV Preview Styles */
.csv-preview {
    max-height: 70vh;
    overflow: auto;
}

.csv-table-container {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    overflow: auto;
    max-height: 60vh;
}

.csv-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.csv-table td {
    padding: 0.5rem 0.75rem;
    border-bottom: 1px solid var(--border-light);
    border-right: 1px solid var(--border-light);
    vertical-align: top;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.csv-table tr:first-child td {
    background: var(--bg-color);
    font-weight: 600;
    color: var(--text-color);
}

.csv-table tr:hover td {
    background: var(--primary-light);
}

/* JSON Preview Styles */
.json-preview {
    max-height: 70vh;
    overflow: auto;
}

.json-content {
    background: #f8fafc;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 1.5rem;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    color: var(--text-color);
    max-height: 60vh;
    overflow: auto;
}

/* Text/Code Preview Styles */
.text-preview {
    max-height: 70vh;
    overflow: auto;
}

.code-preview {
    background: #1e293b;
    color: #e2e8f0;
    border: 1px solid #334155;
}

.code-preview .preview-text {
    background: #1e293b;
    color: #e2e8f0;
    border: 1px solid #334155;
}

/* Enhanced preview media */
.preview-media {
    max-width: 100%;
    max-height: 70vh;
    display: block;
    margin: 0 auto;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-sm);
}

/* 11. Notification System */
.notification-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1100;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-width: 400px;
}

.notification {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-radius: var(--border-radius);
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  cursor: pointer;
  animation: slideIn 0.3s ease-out;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.notification:hover {
  transform: translateX(-4px);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.notification-content svg {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.notification-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  margin-left: 0.5rem;
}

.notification-close:hover {
  opacity: 1;
}

.notification-success {
  background-color: #d4edda;
  color: #155724;
  border-left: 4px solid #28a745;
}

.notification-error {
  background-color: #f8d7da;
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.notification-info {
  background-color: #d1ecf1;
  color: #0c5460;
  border-left: 4px solid #17a2b8;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Document Type Selection Modal */
.document-type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  padding: 1rem 0;
}

.document-type-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  text-align: left;
}

.document-type-card:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.doc-type-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--primary-light);
  border-radius: var(--border-radius-sm);
  color: var(--primary-color);
  flex-shrink: 0;
}

.doc-type-icon svg {
  width: 24px;
  height: 24px;
}

.doc-type-info h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
}

.doc-type-info p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-muted);
  line-height: 1.4;
}

</style>