<template>
  <div class="container">
    <!-- Notification Toast -->
    <div v-if="notification.show" :class="['notification', notification.type]">
      {{ notification.message }}
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="spinner"></div>
      <p>Loading Dashboard...</p>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-message">
      <h2>Something went wrong</h2>
      <p>Could not load your dashboard data. Please try refreshing the page.</p>
      <p class="error-details">Details: {{ error }}</p>
    </div>

    <!-- Main Content (shown after successful load) -->
    <div v-else class="dashboard-content">
      <div class="dashboard-grid">
        <!-- StatsSummary receives the calculated props -->
        <StatsSummary
          :total-accounts="totalAccounts"
          :connected-accounts="connectedAccounts"
          :disconnected-accounts="disconnectedAccounts"
        />
        <!-- StorageVisualization receives storage usage data -->
        <StorageVisualization
          :used-bytes="totalUsedBytes"
          :total-quota-limit-g-b="totalQuotaLimit"
          :storage-accounts="storageAccounts"
          :is-loading="isLoadingStorage"
          @refresh="fetchStorageUsage"
        />
      </div>

      <div class="accounts-section">
        <div class="accounts-header">
          <h2>Your Accounts</h2>
          <button class="add-account-btn" @click="showAddAccountModal = true">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
            <span>Add Account</span>
          </button>
        </div>
        <!-- AccountGrid receives the full list of accounts to display -->
        <AccountGrid :accounts="accounts" />
      </div>
      
      <!-- The modal for adding new accounts -->
      <AddAccountModal
        :is-open="showAddAccountModal"
        @close="showAddAccountModal = false"
        @add-account="handleAddAccount"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
// Import all child components
import StatsSummary from '~/components/dashboard/StatsSummary.vue';
import StorageVisualization from '~/components/dashboard/StorageVisualization.vue';
import AccountGrid from '~/components/dashboard/AccountGrid.vue';
import AddAccountModal from '~/components/dashboard/AddAccountModal.vue';

// --- TYPE DEFINITIONS ---
interface Account {
  id: string; // Or a unique property like 'name'
  name: string;
  isConnected: boolean;
  quotaLimit?: number;
  statusCode?: number;
  connectionError?: string;
  [key: string]: any; // Allow other properties
}

interface StorageAccount {
  accountId: string;
  accountName: string;
  usedBytes: number;
  quotaLimitGB: number;
  isConnected: boolean;
  error?: string;
}

interface Notification {
  show: boolean;
  message: string;
  type: 'success' | 'error';
}

// --- STATE MANAGEMENT (Refs) ---
const accounts = ref<Account[]>([]);
const totalQuotaLimit = ref(0);
const error = ref<string | null>(null);
const isLoading = ref(true);
const showAddAccountModal = ref(false);
const notification = ref<Notification>({ show: false, message: '', type: 'success' });

// Storage-related state
const storageAccounts = ref<StorageAccount[]>([]);
const totalUsedBytes = ref(0);
const isLoadingStorage = ref(false);

// --- COMPUTED PROPERTIES ---
// These automatically derive stats from the main 'accounts' ref.
const totalAccounts = computed(() => accounts.value.length);
const connectedAccounts = computed(() => accounts.value.filter(acc => acc.isConnected).length);
const disconnectedAccounts = computed(() => totalAccounts.value - connectedAccounts.value);


// --- METHODS ---
/**
 * Displays a temporary notification toast.
 */
const showNotification = (message: string, type: 'success' | 'error' = 'success', duration: number = 3000) => {
  notification.value = { show: true, message, type };
  setTimeout(() => {
    notification.value.show = false;
  }, duration);
};

/**
 * Handles the 'add-account' event from the modal.
 */
const handleAddAccount = async (accountData: { name: string; url: string; /* etc */ }) => {
  try {
    const response = await fetch('/api/accounts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(accountData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to add account');
    }

    // Refresh the account list and storage data to show the new one
    await Promise.all([fetchAccounts(), fetchStorageUsage()]);
    showNotification(`Account "${accountData.name}" added successfully!`, 'success');
    showAddAccountModal.value = false;
  } catch (e: any) {
    showNotification(e.message || 'An unknown error occurred.', 'error');
    console.error('Error adding account:', e);
  }
};

/**
 * Fetches all account data from the API endpoint.
 */
async function fetchAccounts() {
  isLoading.value = true;
  error.value = null;
  try {
    const response = await fetch('/api/accounts');
    if (!response.ok) {
      throw new Error(`Server responded with status: ${response.status}`);
    }
    const result = await response.json();
    if (result.error) {
        throw new Error(result.error);
    }
    accounts.value = result.accounts;
    totalQuotaLimit.value = result.totalQuotaLimit;
  } catch (e: any) {
    error.value = e.message || 'An unknown error occurred';
    console.error('Error fetching accounts:', e);
  } finally {
    isLoading.value = false;
  }
}

/**
 * Fetches storage usage data for all accounts
 */
async function fetchStorageUsage() {
  isLoadingStorage.value = true;
  try {
    const response = await fetch('/api/storage-usage');
    if (!response.ok) {
      throw new Error(`Server responded with status: ${response.status}`);
    }
    const result = await response.json();
    if (result.error) {
      console.warn('Storage usage fetch error:', result.error);
      return;
    }

    storageAccounts.value = result.accounts;
    totalUsedBytes.value = result.totalUsedBytes;
    // Update totalQuotaLimit if it's different from accounts API
    if (result.totalQuotaLimitGB !== totalQuotaLimit.value) {
      totalQuotaLimit.value = result.totalQuotaLimitGB;
    }
  } catch (e: any) {
    console.error('Error fetching storage usage:', e);
    // Don't show error to user for storage data, just log it
  } finally {
    isLoadingStorage.value = false;
  }
}

// --- LIFECYCLE HOOK ---
// Fetch data as soon as the component is mounted.
onMounted(async () => {
  await fetchAccounts();
  // Fetch storage usage after accounts are loaded
  await fetchStorageUsage();
});
</script>

<style scoped>
/* All your well-written styles from before go here. No changes needed. */
.container {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  padding: 2rem;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  box-sizing: border-box;
}
.loading-overlay {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 80vh;
  color: #5a677d;
}
.spinner {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: 5px solid #e0e7ff;
  border-top-color: #4f46e5;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}
@keyframes spin { to { transform: rotate(360deg); } }
.error-message {
  color: #9f1239;
  background-color: #ffe4e6;
  border: 1px solid #fecdd3;
  padding: 2rem;
  border-radius: 12px;
  margin: 2rem auto;
  text-align: center;
  max-width: 600px;
}
.error-message h2 { margin-top: 0; color: #be123c; }
.error-details { font-style: italic; color: #881337; font-size: 0.9rem; margin-top: 1rem; }
.notification {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  z-index: 1000;
  animation: slide-down 0.3s ease-out;
}
.notification.success { background-color: #22c55e; }
.notification.error { background-color: #ef4444; }
@keyframes slide-down { from { top: -50px; opacity: 0; } to { top: 20px; opacity: 1; } }
.dashboard-content { display: flex; flex-direction: column; gap: 2rem; animation: fade-in 0.5s ease-in-out; }
@keyframes fade-in { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
.dashboard-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; }
.accounts-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}
.accounts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}
.accounts-section h2 { color: #1a202c; margin: 0; font-size: 1.75rem; font-weight: 700; }
.add-account-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}
.add-account-btn:hover { transform: translateY(-2px); box-shadow: 0 6px 16px rgba(99, 102, 241, 0.3); }
.add-account-btn:active { transform: translateY(0); box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2); }
@media (max-width: 768px) {
  .dashboard-grid { grid-template-columns: 1fr; }
}
@media (max-width: 600px) {
  .container { padding: 1rem; }
  .accounts-header { flex-direction: column; align-items: flex-start; }
  .accounts-section h2 { font-size: 1.5rem; }
}
</style>