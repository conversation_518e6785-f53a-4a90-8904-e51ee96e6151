version: '3.8'

services:
  infinicloud-manager:
    build: .
    container_name: infinicloud-manager
    restart: unless-stopped
    expose:
      - "3000"
    environment:
      - NODE_ENV=production
      - NUXT_HOST=0.0.0.0
      - NUXT_PORT=3000
    volumes:
      # Persistent data storage
      - ./data:/app/data
      - ./logs:/app/logs
      # Optional: Mount config files
      - ./config:/app/config:ro
    networks:
      - infinicloud-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s



networks:
  infinicloud-network:
    driver: bridge

volumes:
  letsencrypt_data:
