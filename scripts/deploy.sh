#!/bin/bash

# Deployment Script
# Run this script to deploy updates to your application

echo "🚀 Deploying InfiniCloud Manager..."

# Pull latest changes (if using git)
# git pull origin main

# Stop containers
echo "🛑 Stopping containers..."
docker-compose down

# Rebuild the application
echo "🔨 Building application..."
docker-compose build

# Start containers
echo "▶️ Starting containers..."
docker-compose up -d

# Wait for containers to be ready
echo "⏳ Waiting for containers to start..."
sleep 10

# Check container status
echo "📊 Container status:"
docker ps

# Test the application
echo "🧪 Testing application..."
if curl -f -s -k https://localhost > /dev/null; then
    echo "✅ Application is running successfully!"
    echo "🌐 Access your app at: https://webmirai.duckdns.org"
else
    echo "❌ Application test failed!"
    echo "📋 Check logs with: docker-compose logs"
fi
