#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to accounts.json
const accountsPath = path.resolve(__dirname, '../data/accounts.json');

console.log('🔄 Migrating existing accounts to support local drive functionality...');

try {
  // Read existing accounts
  const accountsData = fs.readFileSync(accountsPath, 'utf-8');
  const accounts = JSON.parse(accountsData);
  
  console.log(`📁 Found ${accounts.length} accounts to migrate`);
  
  // Migrate each account
  const migratedAccounts = accounts.map(account => {
    const migrated = { ...account };
    
    // Add provider field if missing (assume webdav for existing accounts)
    if (!migrated.provider) {
      migrated.provider = 'webdav';
    }
    
    // Add username field if missing (use name field for webdav accounts)
    if (!migrated.username && migrated.provider === 'webdav') {
      migrated.username = migrated.name;
    }
    
    console.log(`✅ Migrated account: ${migrated.name} (${migrated.provider})`);
    return migrated;
  });
  
  // Create backup of original file
  const backupPath = accountsPath + '.backup.' + Date.now();
  fs.copyFileSync(accountsPath, backupPath);
  console.log(`💾 Created backup at: ${backupPath}`);
  
  // Write migrated accounts
  fs.writeFileSync(accountsPath, JSON.stringify(migratedAccounts, null, 2), 'utf-8');
  
  console.log('✨ Migration completed successfully!');
  console.log(`📊 Migrated ${migratedAccounts.length} accounts`);
  console.log('🔄 Please refresh your browser to see the updated accounts');
  
} catch (error) {
  console.error('❌ Migration failed:', error.message);
  process.exit(1);
}
