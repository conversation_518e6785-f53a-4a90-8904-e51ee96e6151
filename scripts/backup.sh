#!/bin/bash

# Backup Script
# Creates backups of your application data and SSL certificates

BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "💾 Creating backup in $BACKUP_DIR..."

# Backup application data
echo "📁 Backing up application data..."
cp -r data/ "$BACKUP_DIR/data/"

# Backup SSL certificates
echo "🔐 Backing up SSL certificates..."
docker run --rm -v tester_letsencrypt_data:/source -v "$(pwd)/$BACKUP_DIR":/backup alpine \
  tar czf /backup/ssl_certificates.tar.gz -C /source .

# Backup configuration files
echo "⚙️ Backing up configuration..."
cp docker-compose.yml "$BACKUP_DIR/"
cp docker-compose.override.yml "$BACKUP_DIR/"
cp -r nginx/ "$BACKUP_DIR/nginx/"

# Create backup info file
cat > "$BACKUP_DIR/backup_info.txt" << EOF
Backup created: $(date)
Domain: webmirai.duckdns.org
Application: InfiniCloud Manager
Docker containers: $(docker ps --format "table {{.Names}}\t{{.Status}}")
EOF

echo "✅ Backup completed: $BACKUP_DIR"
echo "📦 Backup size: $(du -sh $BACKUP_DIR | cut -f1)"
