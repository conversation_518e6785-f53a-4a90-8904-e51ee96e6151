#!/bin/bash

# SSL Certificate Renewal Script
# Run this script to renew your SSL certificates

echo "🔄 Renewing SSL certificates..."

# Run certbot to renew certificates
docker-compose run --rm certbot certonly --webroot --webroot-path=/var/www/certbot \
  --email <EMAIL> --agree-tos --no-eff-email -d webmirai.duckdns.org \
  --non-interactive

# Reload nginx to use new certificates
echo "🔄 Reloading nginx..."
docker exec infinicloud-nginx nginx -s reload

echo "✅ SSL certificate renewal complete!"
echo "📅 Next renewal needed before: $(date -d '+90 days' '+%Y-%m-%d')"
