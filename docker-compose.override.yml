version: '3.8'

services:
  nginx:
    image: nginx:latest
    container_name: infinicloud-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - letsencrypt_data:/etc/letsencrypt
      - certbot_webroot:/var/www/certbot
    depends_on:
      - infinicloud-manager
    networks:
      - infinicloud-network

# certbot:
  #   image: certbot/certbot
  #   container_name: infinicloud-certbot
  #   volumes:
  #     - letsencrypt_data:/etc/letsencrypt
  #     - certbot_conf:/etc/nginx
  #     - ./nginx/conf.d:/etc/nginx/conf.d:ro
  #     - certbot_webroot:/var/www/certbot
  #   depends_on:
  #     - nginx
  #   command: >
  #      certonly --webroot --webroot-path=/var/www/certbot
  #      --email <EMAIL> --agree-tos --no-eff-email -d webmirai.duckdns.org
  #      --force-renewal --non-interactive
  #   networks:
  #     - infinicloud-network

volumes:
  certbot_conf:
  letsencrypt_data:
  certbot_webroot:
