{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "NODE_OPTIONS='--max-old-space-size=4096' nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@iconify/json": "^2.2.346", "@iconify/vue": "^5.0.0", "@nuxt/icon": "^1.13.0", "@vueuse/core": "^13.3.0", "archiver": "^7.0.1", "nuxt": "^3.17.5", "vue": "^3.5.16", "vue-router": "^4.5.1", "webdav": "^5.8.0", "xlsx": "^0.18.5", "quill": "^2.0.2", "mammoth": "^1.8.0", "html-docx-js": "^0.3.1", "turndown": "^7.2.0"}, "packageManager": "pnpm@10.11.1+sha512.e519b9f7639869dc8d5c3c5dfef73b3f091094b0a006d7317353c72b124e80e1afd429732e28705ad6bfa1ee879c1fce46c128ccebd3192101f43dd67c667912"}