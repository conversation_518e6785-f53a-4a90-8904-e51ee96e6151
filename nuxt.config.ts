// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  components: true, // Enable component auto-discovery
  modules: ['@nuxt/icon'], // Add Nuxt Icon module
  icon: {
    serverBundle: {
      collections: ['mdi'] // Only include Material Design Icons
    }
  },
  build: {
    transpile: ['xlsx'], // Transpile the xlsx package for server-side
  },
    vite: {
    server: {
      allowedHosts: true
    }
  },
  serverHandlers: [
    {
      route: '/api/files/:accountId',
      handler: '~/server/api/files.ts',
    },
    {
      route: '/api/save-file/:accountId/**', // Register the new save endpoint
      handler: '~/server/api/save-file/[accountId]/[...filePath].ts',
    },
    {
      route: '/api/save-document/:accountId/**', // Register the document save endpoint
      handler: '~/server/api/save-document/[accountId]/[...filePath].ts',
    },
    {
      route: '/api/delete-file/:accountId/**', // Register the delete endpoint
      handler: '~/server/api/delete-file/[accountId]/[...filePath].delete.ts',
    },
  ],
})
